{"format": 1, "restore": {"g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Assembly-CSharp.csproj": {}}, "projects": {"g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Editor.csproj": {"projectPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Editor.csproj"}, "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Runtime.csproj": {"projectPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Editor.csproj", "projectName": "NativeGallery.Editor", "projectPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Temp\\obj\\NativeGallery.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Runtime.csproj", "projectName": "NativeGallery.Runtime", "projectPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\NativeGallery.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "g:\\Projects\\CheXuan\\JXT_202507\\QianduanJXT\\Temp\\obj\\NativeGallery.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}