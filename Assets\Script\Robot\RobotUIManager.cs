using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 机器人UI管理器
/// 缓存UI组件引用，统一UI查找逻辑
/// </summary>
public class RobotUIManager
{
    private XYTablePanel _cachedTablePanel;
    private XYPlayerPanel _cachedPlayerPanel;
    private XYSetboboPanel _cachedSetboboPanel;
    private XYZupaiPanel _cachedZupaiPanel;
    private XYOptionPanel _cachedOptionPanel;

    /// <summary>
    /// 获取桌子面板
    /// </summary>
    public XYTablePanel TablePanel
    {
        get
        {
            if (_cachedTablePanel == null)
            {
                _cachedTablePanel = GameObject.FindObjectOfType<XYTablePanel>();
            }
            return _cachedTablePanel;
        }
    }

    /// <summary>
    /// 获取玩家面板
    /// </summary>
    public XYPlayerPanel PlayerPanel
    {
        get
        {
            if (_cachedPlayerPanel == null && TablePanel != null)
            {
                _cachedPlayerPanel = TablePanel.players
                    .SingleOrDefault(m => m.playerid == GameDataHelper.CurrentUserId);
            }
            return _cachedPlayerPanel;
        }
    }

    /// <summary>
    /// 获取设置波波面板
    /// </summary>
    public XYSetboboPanel SetboboPanel
    {
        get
        {
            if (_cachedSetboboPanel == null)
            {
                _cachedSetboboPanel = GameObject.FindObjectOfType<XYSetboboPanel>();
            }
            return _cachedSetboboPanel;
        }
    }

    /// <summary>
    /// 获取组牌面板
    /// </summary>
    public XYZupaiPanel ZupaiPanel
    {
        get
        {
            _cachedZupaiPanel = GameObject.FindObjectOfType<XYZupaiPanel>();
            return _cachedZupaiPanel;
        }
    }

    /// <summary>
    /// 获取操作面板
    /// </summary>
    public XYOptionPanel OptionPanel
    {
        get
        {
            _cachedOptionPanel = GameObject.FindObjectOfType<XYOptionPanel>();
            return _cachedOptionPanel;
        }
    }

    /// <summary>
    /// 获取空座位列表
    /// </summary>
    public List<XYPlayerPanel> GetEmptySeats()
    {
        var sitItemList = GameObject.FindObjectsOfType<XYPlayerPanel>().ToList();
        return sitItemList.Where(m => m.kongweiBtn.gameObject.activeSelf).ToList();
    }

    /// <summary>
    /// 检查组牌面板是否可用
    /// </summary>
    public bool IsZupaiPanelAvailable()
    {
        return ZupaiPanel != null;
    }

    /// <summary>
    /// 检查操作面板是否可用
    /// </summary>
    public bool IsOptionPanelAvailable()
    {
        return OptionPanel != null;
    }

    /// <summary>
    /// 检查设置波波面板是否可用
    /// </summary>
    public bool IsSetboboPanelAvailable()
    {
        return SetboboPanel != null;
    }

    /// <summary>
    /// 检查玩家是否可以回到座位
    /// </summary>
    public bool CanReturnToSeat()
    {
        return PlayerPanel != null && PlayerPanel.huizhuoBtn.gameObject.activeInHierarchy;
    }

    /// <summary>
    /// 清除缓存的UI引用
    /// </summary>
    public void ClearCache()
    {
        _cachedTablePanel = null;
        _cachedPlayerPanel = null;
        _cachedSetboboPanel = null;
        _cachedZupaiPanel = null;
        _cachedOptionPanel = null;
    }

    /// <summary>
    /// 刷新玩家面板缓存
    /// </summary>
    public void RefreshPlayerPanel()
    {
        _cachedPlayerPanel = null;
    }

    /// <summary>
    /// 检查操作按钮是否可用
    /// </summary>
    /// <param name="actionType">操作类型</param>
    /// <returns>按钮是否可用</returns>
    public bool IsActionButtonAvailable(RobotConstants.ActionType actionType)
    {
        var option = OptionPanel;
        if (option == null)
        {
            Debug.LogWarning("操作面板不可用，无法检查按钮状态");
            return false;
        }

        bool isAvailable = false;
        switch (actionType)
        {
            case RobotConstants.ActionType.丢:
                isAvailable = option.diuBtn.gameObject.activeInHierarchy;
                break;
            case RobotConstants.ActionType.休:
                isAvailable = option.xiuBtn.gameObject.activeInHierarchy;
                break;
            case RobotConstants.ActionType.敲:
                isAvailable = option.qiaoBtn.gameObject.activeInHierarchy;
                break;
            case RobotConstants.ActionType.大:
                isAvailable = option.daBtn.gameObject.activeInHierarchy;
                break;
            case RobotConstants.ActionType.拖:
                isAvailable = option.nodaBtn.gameObject.activeInHierarchy;
                break;
            case RobotConstants.ActionType.跟:
                isAvailable = option.genBtn.gameObject.activeInHierarchy;
                break;
            default:
                isAvailable = false;
                break;
        }

        Debug.Log($"按钮可用性检查：{actionType} = {isAvailable}");
        return isAvailable;
    }

    /// <summary>
    /// 执行操作按钮点击
    /// </summary>
    /// <param name="actionType">操作类型</param>
    /// <returns>是否成功执行</returns>
    public bool ExecuteAction(RobotConstants.ActionType actionType)
    {
        var option = OptionPanel;
        if (option == null) return false;

        switch (actionType)
        {
            case RobotConstants.ActionType.丢:
                if (IsActionButtonAvailable(actionType))
                {
                    option.clickDiu();
                    return true;
                }
                break;
            case RobotConstants.ActionType.休:
                if (IsActionButtonAvailable(actionType))
                {
                    option.clickXiu();
                    return true;
                }
                break;
            case RobotConstants.ActionType.敲:
                if (IsActionButtonAvailable(actionType))
                {
                    option.clickQiao();
                    return true;
                }
                break;
            case RobotConstants.ActionType.大:
                if (IsActionButtonAvailable(actionType))
                {
                    // 大操作也使用推荐位（最小推荐位）
                    int bestButtonIndex = SelectBestTuijianButton();
                    option.clickTuijian(bestButtonIndex);
                    return true;
                }
                break;
            case RobotConstants.ActionType.拖:
                if (IsActionButtonAvailable(actionType))
                {
                    // 智能选择推荐按钮（优先选择高倍数）
                    int buttonIndex = SelectHighMultipleTuijianButton();
                    option.clickTuijian(buttonIndex);
                    return true;
                }
                break;
            case RobotConstants.ActionType.跟:
                if (IsActionButtonAvailable(actionType))
                {
                    option.clickGen();
                    return true;
                }
                break;
        }

        return false;
    }

    /// <summary>
    /// 选择最小推荐位（金额最小的可用推荐按钮）
    /// </summary>
    /// <returns>推荐按钮索引（1-3）</returns>
    private int SelectBestTuijianButton()
    {
        var option = OptionPanel;
        if (option == null) return 1;

        try
        {
            // 获取三个推荐按钮的金额和可用性
            int amount1 = int.Parse(option.tuijianT1.text);
            int amount2 = int.Parse(option.tuijianT2.text);
            int amount3 = int.Parse(option.tuijianT3.text);

            bool btn1Available = option.tuijianBtn1.interactable;
            bool btn2Available = option.tuijianBtn2.interactable;
            bool btn3Available = option.tuijianBtn3.interactable;

            // 选择金额最小的可用按钮
            int minAmount = int.MaxValue;
            int selectedButton = 1;

            if (btn1Available && amount1 < minAmount)
            {
                minAmount = amount1;
                selectedButton = 1;
            }

            if (btn2Available && amount2 < minAmount)
            {
                minAmount = amount2;
                selectedButton = 2;
            }

            if (btn3Available && amount3 < minAmount)
            {
                minAmount = amount3;
                selectedButton = 3;
            }

            Debug.Log($"选择最小推荐位：按钮{selectedButton}，金额{minAmount}");
            return selectedButton;
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"解析推荐按钮金额失败：{e.Message}");
        }

        // 默认选择按钮1
        Debug.Log("默认选择推荐按钮1");
        return 1;
    }

    /// <summary>
    /// 随机选择推荐位（用于积极叫价）
    /// </summary>
    /// <returns>推荐按钮索引（1-3）</returns>
    public int SelectRandomTuijianButton()
    {
        var option = OptionPanel;
        if (option == null) return 1;

        // 收集可用的推荐按钮
        var availableButtons = new List<int>();

        if (option.tuijianBtn1.interactable)
            availableButtons.Add(1);
        if (option.tuijianBtn2.interactable)
            availableButtons.Add(2);
        if (option.tuijianBtn3.interactable)
            availableButtons.Add(3);

        if (availableButtons.Count == 0)
        {
            Debug.Log("没有可用的推荐按钮，默认选择按钮1");
            return 1;
        }

        // 随机选择一个可用的按钮
        int randomIndex = UnityEngine.Random.Range(0, availableButtons.Count);
        int selectedButton = availableButtons[randomIndex];

        Debug.Log($"积极叫价：随机选择推荐按钮{selectedButton}（共{availableButtons.Count}个可用）");
        return selectedButton;
    }

    /// <summary>
    /// 执行积极叫价（随机推荐位）
    /// </summary>
    /// <returns>是否成功执行</returns>
    public bool ExecuteAggressiveTuijian()
    {
        var option = OptionPanel;
        if (option == null) return false;

        if (IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            int randomButtonIndex = SelectRandomTuijianButton();
            option.clickTuijian(randomButtonIndex);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 选择高倍数推荐位（用于2-6倍叫价）
    /// </summary>
    /// <returns>推荐按钮索引（1-3）</returns>
    public int SelectHighMultipleTuijianButton()
    {
        var option = OptionPanel;
        if (option == null) return 1;

        try
        {
            // 获取三个推荐按钮的金额和可用性
            int amount1 = int.Parse(option.tuijianT1.text);
            int amount2 = int.Parse(option.tuijianT2.text);
            int amount3 = int.Parse(option.tuijianT3.text);

            bool btn1Available = option.tuijianBtn1.interactable;
            bool btn2Available = option.tuijianBtn2.interactable;
            bool btn3Available = option.tuijianBtn3.interactable;

            Debug.Log($"推荐位金额：按钮1={amount1}({(btn1Available ? "可用" : "不可用")}), " +
                     $"按钮2={amount2}({(btn2Available ? "可用" : "不可用")}), " +
                     $"按钮3={amount3}({(btn3Available ? "可用" : "不可用")})");

            // 收集可用的推荐按钮及其金额
            var availableButtons = new List<(int index, int amount)>();

            if (btn1Available) availableButtons.Add((1, amount1));
            if (btn2Available) availableButtons.Add((2, amount2));
            if (btn3Available) availableButtons.Add((3, amount3));

            if (availableButtons.Count == 0)
            {
                Debug.LogWarning("没有可用的推荐按钮，使用按钮1");
                return 1;
            }

            // 按金额降序排序（金额高的在前）
            availableButtons.Sort((a, b) => b.amount.CompareTo(a.amount));

            // 随机选择策略
            int strategy = UnityEngine.Random.Range(1, 4); // 1-3

            switch (strategy)
            {
                case 1:
                    // 策略1：选择最高金额（最积极）
                    var highest = availableButtons[0];
                    Debug.Log($"高倍叫价策略1：选择最高金额按钮{highest.index}（金额={highest.amount}）");
                    return highest.index;

                case 2:
                    // 策略2：选择中等金额（如果有的话）
                    if (availableButtons.Count >= 2)
                    {
                        var middle = availableButtons[1];
                        Debug.Log($"高倍叫价策略2：选择中等金额按钮{middle.index}（金额={middle.amount}）");
                        return middle.index;
                    }
                    else
                    {
                        var only = availableButtons[0];
                        Debug.Log($"高倍叫价策略2：只有一个可用，选择按钮{only.index}（金额={only.amount}）");
                        return only.index;
                    }

                case 3:
                default:
                    // 策略3：随机选择任意可用按钮
                    var randomChoice = availableButtons[UnityEngine.Random.Range(0, availableButtons.Count)];
                    Debug.Log($"高倍叫价策略3：随机选择按钮{randomChoice.index}（金额={randomChoice.amount}）");
                    return randomChoice.index;
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"选择高倍推荐位时出错：{ex.Message}，使用默认按钮1");
            return 1;
        }
    }
}
