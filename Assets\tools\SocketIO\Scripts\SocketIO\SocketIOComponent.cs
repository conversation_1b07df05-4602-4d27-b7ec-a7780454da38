﻿#region License
/*
 * SocketIO.cs
 *
 * The MIT License
 *
 * Copyright (c) 2014 Fabio Panettieri
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

#endregion

//#define SOCKET_IO_DEBUG			// Uncomment this for debug
using System;
using System.Collections;
using System.Collections.Generic; 
using System.Threading;
using UnityEngine;
using UnityEngine.SocialPlatforms;
using UnityEngine.UI;
using WebSocketSharp;
using WebSocketSharp.Net;
using static SocketIO.SocketIOComponent;

namespace SocketIO
{
    
	public enum IO
    {

	}
    
	public class SocketIOComponent : MonoBehaviour
	{
		public enum Address
		{
			外网, 局域网, 本地
		}

		//public Address address=Address.本地;
		#region Public Properties

		private string url = "";
        public string HttpURL { get; set; }
        public bool autoConnect = true;
		public int reconnectDelay = 5;
		public float ackExpirationTime = 1800f;
		public float pingInterval = 25f;
		public float pingTimeout = 60f;

		public WebSocket socket { get { return ws; } }
		public string sid { get; set; }
		public bool IsConnected { get { return connected; } }

		#endregion

		#region Private Properties

		private volatile bool connected;
		private volatile bool thPinging;
		private volatile bool thPong;
		private volatile bool wsConnected;

		private Thread socketThread;
		private Thread pingThread;
		private WebSocket ws;
        
		private Dictionary<string, List<Action<SocketIOEvent>>> handlers;
		private object eventQueueLock;
		private Queue<SocketIOEvent> eventQueue;

        #endregion

#if SOCKET_IO_DEBUG
		public Action<string> debugMethod;
#endif

        #region Unity interface
      
	  	public void Awake()
        {


// #if UNITY_EDITOR
			PlayerPrefs.SetString("address", Address.局域网.ToString());

			url = "ws://**************:8051/19192";
			HttpURL = "http://**************:8881/XyqAdmin_war/";
// #else
//  			PlayerPrefs.SetString("address", Address.外网.ToString());


//  			url = "ws://**************:8051/19192";
//  			HttpURL = "http://**************:8881/XyqAdmin_war/";
// #endif
			PlayerPrefs.SetString("url", url);
            handlers = new Dictionary<string, List<Action<SocketIOEvent>>>();
			sid = null;
			ws = new WebSocket(url);
			ws.OnOpen += OnOpen;
			ws.OnMessage += OnMessage;
			ws.OnError += OnError;
			ws.OnClose += OnClose;
			wsConnected = false;

			eventQueueLock = new object();
			eventQueue = new Queue<SocketIOEvent>();

			connected = false;

#if SOCKET_IO_DEBUG
			if(debugMethod == null) { debugMethod = Debug.Log; };
#endif
		}

		public void Start()
		{
            if (autoConnect)
            {
                Connect();
            }
		}

		public void Update()
		{
			lock(eventQueueLock){ 
				while(eventQueue.Count > 0){
					EmitEvent(eventQueue.Dequeue());
				}
			}

			if(wsConnected != ws.IsConnected){
				wsConnected = ws.IsConnected;
				if(wsConnected){
					EmitEvent(new Packet(PacketType.CONNECT));
				} else {
					EmitEvent(new Packet(PacketType.DISCONNECT));
				}
			}
		}

		public void OnDestroy()
		{
			if (socketThread != null) 	{ socketThread.Abort(); }
			if (pingThread != null) 	{ pingThread.Abort(); }
		}

		public void OnApplicationQuit()
		{
			Close();
		}

#endregion

		#region Public Interface
		
		public void Connect()
		{
			connected = true;

			socketThread = new Thread(RunSocketThread);
			socketThread.Start(ws);

			pingThread = new Thread(RunPingThread);
			pingThread.Start(ws);
		}

		public void Close()
		{
			EmitClose();
			connected = false;
		}

		public void On(string ev, Action<SocketIOEvent> callback)
		{
			if (!handlers.ContainsKey(ev))
            {
				handlers[ev] = new List<Action<SocketIOEvent>>();
			}
			handlers[ev].Add(callback);
		}

		public void Off(string ev, Action<SocketIOEvent> callback)
		{
			if (!handlers.ContainsKey(ev)) {
#if SOCKET_IO_DEBUG
				debugMethod.Invoke("[SocketIO] No callbacks registered for event: " + ev);
#endif
				return;
			}

			List<Action<SocketIOEvent>> l = handlers [ev];
			if (!l.Contains(callback)) {
#if SOCKET_IO_DEBUG
				debugMethod.Invoke("[SocketIO] Couldn't remove callback action for event: " + ev);
#endif
				return;
			}

			l.Remove(callback);
			if (l.Count == 0) {
				handlers.Remove(ev);
			}
		}

		public void Emit(Packet pack)
		{
			EmitMessage(pack);
		}

		#endregion

		#region Private Methods

		private void RunSocketThread(object obj)
		{
			WebSocket webSocket = (WebSocket)obj;
			while(connected){
				if(webSocket.IsConnected){
					Thread.Sleep(reconnectDelay);
				} else {
					webSocket.Connect();
				}
			}
			webSocket.Close();
		}

		private void RunPingThread(object obj)
		{
			WebSocket webSocket = (WebSocket)obj;

			int timeoutMilis = Mathf.FloorToInt(pingTimeout * 1000);
			int intervalMilis = Mathf.FloorToInt(pingInterval * 1000);

			DateTime pingStart;

			while(connected)
			{
				if(!wsConnected){
					Thread.Sleep(reconnectDelay);
				} else {
					thPinging = true;
					thPong =  false;
					pingStart = DateTime.Now;
					while(webSocket.IsConnected && thPinging && (DateTime.Now.Subtract(pingStart).TotalSeconds < timeoutMilis)){
						Thread.Sleep(200);
					}
					if(!thPong){
						webSocket.Close();
					}

					Thread.Sleep(intervalMilis);
				}
			}
		}

		private void EmitMessage(Packet pack)
		{
			EmitPacket(pack);
		}

		private void EmitClose()
        {
            //EmitPacket(new Packet(PacketType.CLOSE));
		}

		private void EmitPacket(Packet packet)
		{
#if SOCKET_IO_DEBUG
			debugMethod.Invoke("[SocketIO] " + packet);
#endif
			
			try {
                packet.Serialization();
                if(packet.index > 0)
                {
                    byte[] byt = new byte[packet.index];
                    Array.ConstrainedCopy(packet.bytes, 0, byt, 0, packet.index);
                    ws.Send(byt);
                }
			}
            catch (SocketIOException ee) {
                if (ee == null)
                {

                }
#if SOCKET_IO_DEBUG
				debugMethod.Invoke(ee.ToString());
#endif
            }
		}

		private void OnOpen(object sender, EventArgs e)
		{
			EmitEvent(new Packet(PacketType.OPEN));
		}

		private void OnMessage(object sender, MessageEventArgs e)
		{
#if SOCKET_IO_DEBUG
			debugMethod.Invoke("[SocketIO] Raw message: " + e.Data);
#endif
			Packet packet =new Packet(e.RawData);
            HandleMessage(packet);
            //switch (packet.enginePacketType) {
            //	case EnginePacketType.CONNECT: 	HandleOpen(packet);		break;
            //	case EnginePacketType.CLOSE: 	EmitEvent(new Packet(EnginePacketType.CLOSE));		break;
            //	case EnginePacketType.PING:		HandlePing();	   		break;
            //	case EnginePacketType.PONG:		HandlePong();	   		break;
            //	case EnginePacketType.MESSAGE: 	HandleMessage(packet);	break;
            //}
        }

		private void HandleOpen(Packet packet)
		{
#if SOCKET_IO_DEBUG
			debugMethod.Invoke("[SocketIO] Socket.IO sid: " + packet.enginePacketType.ToString());
#endif
            sid = packet.enginePacketType.ToString();
			EmitEvent(new Packet(PacketType.OPEN));
		}

		private void HandlePing()
		{
			//EmitPacket(new Packet(PacketType.PONG));
		}

		private void HandlePong()
		{
			thPong = true;
			thPinging = false;
		}
		
		private void HandleMessage(Packet packet)
		{
			if(packet.bytes.Length <=0) { return; }
			if (packet.enginePacketType != PacketType.HeartbeatRsp)
			{
                UnityEngine.Debug.Log("<color=#F17AFF>收到返回" + packet.enginePacketType.ToString() + " = " + (int)packet.enginePacketType+"</color>");
			}
			if (packet.enginePacketType >(PacketType)101)
            {
                SocketIOEvent e = new SocketIOEvent(packet);
                lock (eventQueueLock) { eventQueue.Enqueue(e); }

#if SOCKET_IO_DEBUG
				debugMethod.Invoke("[SocketIO] Ack received for invalid Action: " +packet.enginePacketType);
#endif
            }
		}

		private void OnError(object sender, ErrorEventArgs e)
		{
			EmitEvent(new Packet(PacketType.ERROR));
		}

		private void OnClose(object sender, CloseEventArgs e)
		{
			EmitEvent(new Packet(PacketType.CLOSE));
		}

		private void EmitEvent(Packet pack)
		{
            EmitEvent(new SocketIOEvent(pack));
		}

		private void EmitEvent(SocketIOEvent ev)
		{
			if (!handlers.ContainsKey(ev.pack.enginePacketType.ToString())) { return; }
			foreach (Action<SocketIOEvent> handler in this.handlers[ev.pack.enginePacketType.ToString()]) {
				try{
					handler(ev);
				} catch(Exception ex){
                    game.logmanager.LogError(ex.Message);
                    game.logmanager.LogError(ex.StackTrace);
				}
			}
		}
		#endregion
	}
}
