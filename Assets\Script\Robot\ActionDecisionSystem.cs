using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 操作决策系统
/// 统一处理机器人的操作选择逻辑
/// </summary>
public class ActionDecisionSystem
{
    private readonly RobotUIManager _uiManager;

    // 叫价次数跟踪
    private int _currentRoundBettingCount = 0;
    private const int MAX_AGGRESSIVE_BETTING = 1; // 最多积极叫价1次

    public ActionDecisionSystem(RobotUIManager uiManager)
    {
        _uiManager = uiManager;
    }

    /// <summary>
    /// 根据牌型决定发牌阶段的操作
    /// </summary>
    /// <param name="cardType">牌型信息</param>
    /// <param name="cards">手牌</param>
    /// <returns>选择的操作类型</returns>
    public RobotConstants.ActionType DecideFapaiAction(XYCardType cardType, List<PaperCardStructrue> cards)
    {
        Debug.LogWarning($"发牌阶段决策：手牌数量={cards?.Count ?? 0}");

        // 打印手牌信息用于调试
        if (cards != null && cards.Count > 0)
        {
            var cardInfo = string.Join(", ", cards.Select(c => $"{c.num}"));
            Debug.LogWarning($"当前手牌：{cardInfo}");
        }

        // 每发新牌都要检测牌力和策略
        return AnalyzeCardsAndDecide(cards);
    }

    /// <summary>
    /// 分析当前手牌并决定策略
    /// </summary>
    /// <param name="cards">当前手牌</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType AnalyzeCardsAndDecide(List<PaperCardStructrue> cards)
    {
        if (cards == null || cards.Count < 2)
        {
            Debug.Log("手牌不足，使用兜底策略");
            return GetFinalFallbackAction();
        }

        // 使用灵活决策系统
        var flexibleAction = FlexibleDecisionSystem(cards);
        if (flexibleAction != RobotConstants.ActionType.空)
        {
            Debug.Log($"使用灵活决策：{flexibleAction}");
            return flexibleAction;
        }

        // 灵活系统无法决策时，使用兜底策略
        Debug.Log("灵活决策无法处理，使用最终兜底策略");
        return GetFinalFallbackAction();
    }

    /// <summary>
    /// 灵活决策系统主入口
    /// </summary>
    /// <param name="cards">当前手牌</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType FlexibleDecisionSystem(List<PaperCardStructrue> cards)
    {
        // 2-3张牌：简单灵活规则
        if (cards.Count < 4)
        {
            return EarlyFlexibleDecision(cards);
        }

        // 4张牌：头尾牌全面比较
        return FinalFlexibleDecision(cards);
    }

    /// <summary>
    /// 早期灵活决策（2-3张牌）
    /// </summary>
    /// <param name="cards">当前手牌</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType EarlyFlexibleDecision(List<PaperCardStructrue> cards)
    {
        // 更新叫价次数
        UpdateBettingCount();

        // 有Q(12)或2点：但要考虑叫价次数
        if (HasQOr2Cards(cards))
        {
            if (_currentRoundBettingCount <= MAX_AGGRESSIVE_BETTING)
            {
                Debug.Log($"检测到Q或2点，第{_currentRoundBettingCount}次叫价，选择'大'");
                return GetFlexibleAction(RobotConstants.ActionType.大, "Q或2点");
            }
            else
            {
                Debug.Log($"检测到Q或2点，但已叫价{_currentRoundBettingCount}次，改为保守跟牌");
                return GetFlexibleAction(RobotConstants.ActionType.跟, "Q或2点但已多次叫价");
            }
        }

        // 检查当前最佳牌型
        var bestCardType = GetMyBestCardType(cards);
        if (bestCardType != null)
        {
            // 高价值牌型：但要考虑叫价次数
            if (IsHighValueCardType(bestCardType, bestCardType.value))
            {
                if (_currentRoundBettingCount <= MAX_AGGRESSIVE_BETTING)
                {
                    Debug.Log($"检测到高价值牌型：{bestCardType.typename}，第{_currentRoundBettingCount}次叫价，2-4倍叫价");
                    return GetFlexibleMultipleBetting($"高价值牌型：{bestCardType.typename}");
                }
                else
                {
                    Debug.Log($"检测到高价值牌型：{bestCardType.typename}，但已叫价{_currentRoundBettingCount}次，改为保守跟牌");
                    return GetFlexibleAction(RobotConstants.ActionType.跟, $"高价值牌型但已多次叫价：{bestCardType.typename}");
                }
            }

            // 9点：选择"跟"
            if (bestCardType.typename == "9点")
            {
                Debug.Log("检测到9点，选择'跟'");
                return GetFlexibleAction(RobotConstants.ActionType.跟, "9点");
            }

            // 8点以下：选择"丢"
            if (IsLowPoints(bestCardType))
            {
                Debug.Log($"检测到低点数：{bestCardType.typename}，选择'丢'");
                return GetFlexibleAction(RobotConstants.ActionType.丢, $"低点数：{bestCardType.typename}");
            }
        }

        // 其他情况：保守丢牌
        Debug.Log("早期决策：其他情况，保守丢牌");
        return GetFlexibleAction(RobotConstants.ActionType.丢, "其他情况保守");
    }

    /// <summary>
    /// 更新叫价次数（基于当前游戏状态）
    /// </summary>
    private void UpdateBettingCount()
    {
        var roomData = GameDataHelper.RoomData;
        if (roomData == null) return;

        // 获取当前最大叫价金额，用来判断是否有新的叫价轮次
        var currentMaxBet = roomData.operateMaxScore;
        var difen = roomData.difen;

        // 简单的叫价次数估算：(当前最大叫价 - 底分) / 底分
        // 这样可以大概估算出已经叫价了多少轮
        if (currentMaxBet > difen)
        {
            _currentRoundBettingCount = (currentMaxBet - difen) / difen + 1;
        }
        else
        {
            _currentRoundBettingCount = 0; // 还没开始叫价
        }

        Debug.Log($"叫价次数更新：当前最大叫价={currentMaxBet}, 底分={difen}, 估算叫价次数={_currentRoundBettingCount}");
    }

    /// <summary>
    /// 重置叫价次数（新游戏开始时调用）
    /// </summary>
    public void ResetBettingCount()
    {
        _currentRoundBettingCount = 0;
        Debug.Log("叫价次数已重置");
    }

    /// <summary>
    /// 检查是否有Q(12)或2点
    /// </summary>
    /// <param name="cards">手牌</param>
    /// <returns>是否有Q或2</returns>
    private bool HasQOr2Cards(List<PaperCardStructrue> cards)
    {
        if (cards == null) return false;

        foreach (var card in cards)
        {
            if (card.num == 12 || card.num == 2) // Q=12, 2=2
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 检查是否为8点以下的低点数
    /// </summary>
    /// <param name="cardType">牌型</param>
    /// <returns>是否为低点数</returns>
    private bool IsLowPoints(XYCardType cardType)
    {
        if (cardType == null) return true; // 没有牌型算低点数

        // 8点以下的牌型
        string[] lowPointTypes = { "0点", "1点", "2点", "3点", "4点", "5点", "6点", "7点", "8点" };

        foreach (var lowType in lowPointTypes)
        {
            if (cardType.typename == lowType)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 2-4倍随机叫价
    /// </summary>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetMultipleBettingAction()
    {
        // 优先使用推荐按钮进行2-4倍叫价
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("高价值牌型：使用推荐位进行2-4倍叫价");
            return RobotConstants.ActionType.拖;
        }

        // 备选：使用大
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("高价值牌型：使用'大'");
            return RobotConstants.ActionType.大;
        }

        // 最后备选：敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("高价值牌型：使用'敲'");
            return RobotConstants.ActionType.敲;
        }

        return RobotConstants.ActionType.空;
    }

    /// <summary>
    /// 灵活操作选择（带备选方案）
    /// </summary>
    /// <param name="preferredAction">首选操作</param>
    /// <param name="reason">选择原因</param>
    /// <returns>可执行的操作类型</returns>
    private RobotConstants.ActionType GetFlexibleAction(RobotConstants.ActionType preferredAction, string reason)
    {
        // 首选操作可用，直接使用
        if (_uiManager.IsActionButtonAvailable(preferredAction))
        {
            Debug.Log($"灵活决策：使用首选操作'{preferredAction}'（{reason}）");
            return preferredAction;
        }

        Debug.Log($"灵活决策：首选操作'{preferredAction}'不可用，寻找备选方案（{reason}）");

        // 根据首选操作提供备选方案
        switch (preferredAction)
        {
            case RobotConstants.ActionType.大:
                // 大不可用：拖 > 敲 > 跟 > 丢
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
                {
                    Debug.Log("备选：使用'拖'代替'大'");
                    return RobotConstants.ActionType.拖;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
                {
                    Debug.Log("备选：使用'敲'代替'大'");
                    return RobotConstants.ActionType.敲;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
                {
                    Debug.Log("备选：使用'跟'代替'大'");
                    return RobotConstants.ActionType.跟;
                }
                break;

            case RobotConstants.ActionType.跟:
                // 跟不可用：拖 > 大 > 敲 > 丢
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
                {
                    Debug.Log("备选：使用'拖'代替'跟'");
                    return RobotConstants.ActionType.拖;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
                {
                    Debug.Log("备选：使用'大'代替'跟'");
                    return RobotConstants.ActionType.大;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
                {
                    Debug.Log("备选：使用'敲'代替'跟'");
                    return RobotConstants.ActionType.敲;
                }
                break;

            case RobotConstants.ActionType.丢:
                // 丢不可用：休 > 跟 > 拖
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.休))
                {
                    Debug.Log("备选：使用'休'代替'丢'");
                    return RobotConstants.ActionType.休;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
                {
                    Debug.Log("备选：使用'跟'代替'丢'（被迫）");
                    return RobotConstants.ActionType.跟;
                }
                if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
                {
                    Debug.Log("备选：使用'拖'代替'丢'（被迫）");
                    return RobotConstants.ActionType.拖;
                }
                break;
        }

        // 最终兜底：按优先级选择任何可用操作
        Debug.Log("所有备选方案都不可用，使用最终兜底策略");
        return GetFinalFallbackAction();
    }

    /// <summary>
    /// 灵活的2-4倍叫价（带备选方案）
    /// </summary>
    /// <param name="reason">叫价原因</param>
    /// <returns>可执行的操作类型</returns>
    private RobotConstants.ActionType GetFlexibleMultipleBetting(string reason)
    {
        // 优先使用推荐按钮进行2-4倍叫价
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log($"灵活叫价：使用'拖'进行2-4倍叫价（{reason}）");
            return RobotConstants.ActionType.拖;
        }

        // 备选：使用大
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log($"灵活叫价：使用'大'代替推荐位（{reason}）");
            return RobotConstants.ActionType.大;
        }

        // 备选：使用敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log($"灵活叫价：使用'敲'代替推荐位（{reason}）");
            return RobotConstants.ActionType.敲;
        }

        // 备选：使用跟
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log($"灵活叫价：使用'跟'代替推荐位（{reason}）");
            return RobotConstants.ActionType.跟;
        }

        // 最终兜底
        Debug.Log($"灵活叫价：所有叫价操作都不可用，使用兜底策略（{reason}）");
        return GetFinalFallbackAction();
    }

    /// <summary>
    /// 最终兜底策略：按优先级选择任何可用操作
    /// </summary>
    /// <returns>可执行的操作类型</returns>
    private RobotConstants.ActionType GetFinalFallbackAction()
    {
        // 按优先级尝试所有操作：拖 > 跟 > 大 > 敲 > 休 > 丢
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("最终兜底：使用'拖'");
            return RobotConstants.ActionType.拖;
        }
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("最终兜底：使用'跟'");
            return RobotConstants.ActionType.跟;
        }
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("最终兜底：使用'大'");
            return RobotConstants.ActionType.大;
        }
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("最终兜底：使用'敲'");
            return RobotConstants.ActionType.敲;
        }
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.休))
        {
            Debug.Log("最终兜底：使用'休'");
            return RobotConstants.ActionType.休;
        }
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.丢))
        {
            Debug.Log("最终兜底：使用'丢'");
            return RobotConstants.ActionType.丢;
        }

        Debug.LogError("最终兜底：没有任何可用操作！");
        return RobotConstants.ActionType.丢; // 强制返回丢，即使不可用
    }

    /// <summary>
    /// 灵活的积极叫价（带备选方案）
    /// </summary>
    /// <param name="reason">叫价原因</param>
    /// <returns>可执行的操作类型</returns>
    private RobotConstants.ActionType GetFlexibleAggressiveAction(string reason)
    {
        // 积极叫价优先级：敲 > 拖 > 大 > 跟
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log($"灵活积极叫价：使用'敲'（{reason}）");
            return RobotConstants.ActionType.敲;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log($"灵活积极叫价：使用'拖'代替'敲'（{reason}）");
            return RobotConstants.ActionType.拖;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log($"灵活积极叫价：使用'大'代替'敲'（{reason}）");
            return RobotConstants.ActionType.大;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log($"灵活积极叫价：使用'跟'代替'敲'（{reason}）");
            return RobotConstants.ActionType.跟;
        }

        // 最终兜底
        Debug.Log($"灵活积极叫价：所有积极操作都不可用，使用兜底策略（{reason}）");
        return GetFinalFallbackAction();
    }

    /// <summary>
    /// 最终灵活决策（4张牌时的头尾牌比较）
    /// </summary>
    /// <param name="cards">当前手牌</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType FinalFlexibleDecision(List<PaperCardStructrue> cards)
    {
        Debug.Log("4张牌最终决策：开始头尾牌比较");

        // 更新叫价次数
        UpdateBettingCount();

        // 计算机器人最佳分牌方案
        var myBestSplit = GetMyBestSplit(cards);
        if (myBestSplit == null)
        {
            Debug.Log("无法计算机器人最佳分牌，直接丢牌");
            return GetFlexibleAction(RobotConstants.ActionType.丢, "无法计算最佳分牌");
        }

        // 计算对手最强可能的分牌组合
        var opponentBestSplit = GetOpponentStrongestSplit();
        if (opponentBestSplit == null)
        {
            Debug.Log("无法获取对手牌型，根据自己牌力决策");
            // 如果是高价值牌型就继续，但要考虑叫价次数
            if (IsHighValueCardType(myBestSplit.BestCardType, myBestSplit.BestCardType.value))
            {
                if (_currentRoundBettingCount <= MAX_AGGRESSIVE_BETTING)
                {
                    Debug.Log($"对手信息不足，自己是高价值牌型，第{_currentRoundBettingCount}次叫价");
                    return GetFlexibleAggressiveAction("对手信息不足，自己是高价值牌型");
                }
                else
                {
                    Debug.Log($"对手信息不足，自己是高价值牌型，但已叫价{_currentRoundBettingCount}次，改为跟牌");
                    return GetFlexibleAction(RobotConstants.ActionType.跟, "高价值牌型但已多次叫价");
                }
            }
            return GetFlexibleAction(RobotConstants.ActionType.丢, "对手信息不足，自己牌力一般");
        }

        // 比较：机器人最佳 vs 对手最强
        if (CanWinAgainstOpponentBest(myBestSplit, opponentBestSplit))
        {
            if (_currentRoundBettingCount <= MAX_AGGRESSIVE_BETTING)
            {
                Debug.Log($"能赢对手最强组合，第{_currentRoundBettingCount}次叫价，继续积极叫价");
                return GetFlexibleAggressiveAction("能赢对手最强组合");
            }
            else
            {
                Debug.Log($"能赢对手最强组合，但已叫价{_currentRoundBettingCount}次，改为保守跟牌");
                return GetFlexibleAction(RobotConstants.ActionType.跟, "能赢但已多次叫价");
            }
        }
        else
        {
            Debug.Log("打不过对手最强组合，直接丢牌");
            return GetFlexibleAction(RobotConstants.ActionType.丢, "打不过对手最强组合");
        }
    }

    /// <summary>
    /// 分牌结果结构
    /// </summary>
    private class SplitResult
    {
        public XYCardType HeadCardType { get; set; }  // 头牌牌型
        public XYCardType TailCardType { get; set; }  // 尾牌牌型
        public XYCardType BestCardType { get; set; }  // 最佳牌型（头尾中较好的）
        public List<PaperCardStructrue> HeadCards { get; set; }  // 头牌
        public List<PaperCardStructrue> TailCards { get; set; }  // 尾牌
    }

    /// <summary>
    /// 获取机器人最佳分牌方案
    /// </summary>
    /// <param name="cards">机器人4张牌</param>
    /// <returns>最佳分牌结果</returns>
    private SplitResult GetMyBestSplit(List<PaperCardStructrue> cards)
    {
        if (cards == null || cards.Count != 4) return null;

        // 使用现有的分牌策略管理器
        var strategyManager = new CardSplitStrategyManager();
        var cardsArray = cards.ToArray();
        var (index1, index2) = strategyManager.GetBestSplit(cardsArray, GameDataHelper.IsDijiuwangEnabled);

        // 构建头牌和尾牌
        var headCards = new List<PaperCardStructrue> { cardsArray[index1], cardsArray[index2] };
        var tailCards = new List<PaperCardStructrue>();

        for (int i = 0; i < 4; i++)
        {
            if (i != index1 && i != index2)
            {
                tailCards.Add(cardsArray[i]);
            }
        }

        // 计算头尾牌型
        var headCardType = XYAllCardType.checkCardType(headCards, GameDataHelper.IsDijiuwangEnabled);
        var tailCardType = XYAllCardType.checkCardType(tailCards, GameDataHelper.IsDijiuwangEnabled);

        // 选择较好的牌型作为最佳牌型
        var bestCardType = headCardType;
        if (tailCardType != null && (headCardType == null || tailCardType.value > headCardType.value))
        {
            bestCardType = tailCardType;
        }

        Debug.Log($"机器人分牌：头牌={headCardType?.typename ?? "无"}({headCardType?.value ?? 0}), 尾牌={tailCardType?.typename ?? "无"}({tailCardType?.value ?? 0})");

        return new SplitResult
        {
            HeadCardType = headCardType,
            TailCardType = tailCardType,
            BestCardType = bestCardType,
            HeadCards = headCards,
            TailCards = tailCards
        };
    }

    /// <summary>
    /// 获取对手最强可能的分牌组合
    /// </summary>
    /// <returns>对手最强分牌结果</returns>
    private SplitResult GetOpponentStrongestSplit()
    {
        var roomData = GameDataHelper.RoomData;
        if (roomData?.players == null) return null;

        var myUserId = GameDataHelper.CurrentUserId;
        SplitResult strongestSplit = null;

        foreach (var player in roomData.players)
        {
            // 跳过自己和已丢牌的玩家
            if (player.id == myUserId || player.operationType == 0 || player.ready != 1)
                continue;

            // 检查对手是否有4张牌
            if (player.handCards != null && player.handCards.Count >= 4)
            {
                var opponentCards = player.handCards.ToArray();
                var playerStrongestSplit = GetPlayerStrongestSplit(opponentCards);

                if (playerStrongestSplit != null)
                {
                    if (strongestSplit == null ||
                        (playerStrongestSplit.BestCardType != null &&
                         (strongestSplit.BestCardType == null || playerStrongestSplit.BestCardType.value > strongestSplit.BestCardType.value)))
                    {
                        strongestSplit = playerStrongestSplit;
                        Debug.Log($"发现更强对手：玩家{player.id}，最强牌型={playerStrongestSplit.BestCardType?.typename}({playerStrongestSplit.BestCardType?.value})");
                    }
                }
            }
        }

        return strongestSplit;
    }

    /// <summary>
    /// 计算单个玩家所有可能分牌中的最强组合
    /// </summary>
    /// <param name="cards">玩家4张牌</param>
    /// <returns>最强分牌结果</returns>
    private SplitResult GetPlayerStrongestSplit(PaperCardStructrue[] cards)
    {
        if (cards == null || cards.Length != 4) return null;

        SplitResult strongestSplit = null;

        // 遍历所有可能的分牌组合 C(4,2) = 6种
        for (int i = 0; i < 4; i++)
        {
            for (int j = i + 1; j < 4; j++)
            {
                // 头牌：第i和第j张
                var headCards = new List<PaperCardStructrue> { cards[i], cards[j] };
                var tailCards = new List<PaperCardStructrue>();

                // 尾牌：剩余的两张
                for (int k = 0; k < 4; k++)
                {
                    if (k != i && k != j)
                    {
                        tailCards.Add(cards[k]);
                    }
                }

                // 计算头尾牌型
                var headCardType = XYAllCardType.checkCardType(headCards, GameDataHelper.IsDijiuwangEnabled);
                var tailCardType = XYAllCardType.checkCardType(tailCards, GameDataHelper.IsDijiuwangEnabled);

                // 选择较好的牌型作为该分牌的代表牌型
                var bestCardType = headCardType;
                if (tailCardType != null && (headCardType == null || tailCardType.value > headCardType.value))
                {
                    bestCardType = tailCardType;
                }

                var currentSplit = new SplitResult
                {
                    HeadCardType = headCardType,
                    TailCardType = tailCardType,
                    BestCardType = bestCardType,
                    HeadCards = headCards,
                    TailCards = tailCards
                };

                // 比较是否是最强分牌
                if (strongestSplit == null ||
                    (bestCardType != null &&
                     (strongestSplit.BestCardType == null || bestCardType.value > strongestSplit.BestCardType.value)))
                {
                    strongestSplit = currentSplit;
                }
            }
        }

        return strongestSplit;
    }

    /// <summary>
    /// 判断机器人是否能赢过对手最强组合（头牌和尾牌都要赢）
    /// </summary>
    /// <param name="mySplit">机器人分牌</param>
    /// <param name="opponentSplit">对手分牌</param>
    /// <returns>是否能赢</returns>
    private bool CanWinAgainstOpponentBest(SplitResult mySplit, SplitResult opponentSplit)
    {
        if (mySplit == null || opponentSplit == null) return false;

        // 头牌比较
        int myHeadValue = mySplit.HeadCardType?.value ?? 0;
        int opponentHeadValue = opponentSplit.HeadCardType?.value ?? 0;
        bool headWin = myHeadValue > opponentHeadValue;

        // 尾牌比较
        int myTailValue = mySplit.TailCardType?.value ?? 0;
        int opponentTailValue = opponentSplit.TailCardType?.value ?? 0;
        bool tailWin = myTailValue > opponentTailValue;

        Debug.Log($"头尾牌比较：我方头牌({myHeadValue}) vs 对手头牌({opponentHeadValue}) = {(headWin ? "胜" : "负")}");
        Debug.Log($"头尾牌比较：我方尾牌({myTailValue}) vs 对手尾牌({opponentTailValue}) = {(tailWin ? "胜" : "负")}");

        // 必须头牌和尾牌都赢才算胜利
        bool canWin = headWin && tailWin;
        Debug.Log($"最终结果：{(canWin ? "能赢" : "不能赢")}（头尾都要赢）");

        return canWin;
    }

    /// <summary>
    /// 判断是否为高级牌型
    /// </summary>
    /// <param name="cardType">牌型</param>
    /// <param name="cardValue">牌型权重</param>
    /// <returns>是否为高级牌型</returns>
    private bool IsHighValueCardType(XYCardType cardType, int cardValue)
    {
        if (cardType == null) return false;

        // 定义高级牌型的权重阈值
        const int HIGH_VALUE_THRESHOLD = 185; // 豹子9对子及以上

        // 权重≥185的都是高级牌型，应该积极叫价
        if (cardValue >= HIGH_VALUE_THRESHOLD)
        {
            return true;
        }

        // 特殊检查：Q+2组合（丁皇）
        if (cardValue == 200 && cardType.typename == "丁皇")
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 积极叫价策略：随机选择推荐位
    /// </summary>
    /// <param name="reason">叫价原因</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetAggressiveAction(string reason)
    {
        // 积极叫价：优先使用推荐位（随机选择）
        if (_uiManager.ExecuteAggressiveTuijian())
        {
            Debug.Log($"使用 拖（随机推荐位）({reason})");
            return RobotConstants.ActionType.拖;
        }

        // 如果推荐位不可用，使用其他积极策略
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log($"使用 大 ({reason})");
            return RobotConstants.ActionType.大;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log($"使用 敲 ({reason})");
            return RobotConstants.ActionType.敲;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log($"使用 跟 ({reason})");
            return RobotConstants.ActionType.跟;
        }
        else
        {
            return GetSimpleFapaiAction();
        }
    }



    /// <summary>
    /// 第四张牌的最终决策：检查对手可见对子
    /// </summary>
    /// <param name="myBestCardType">自己最佳牌型</param>
    /// <param name="myMaxValue">自己最大权重</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType FinalDecisionWithOpponentCheck(XYCardType myBestCardType, int myMaxValue)
    {
        // 获取对手最强可见对子的权重
        var opponentBestPair = GetOpponentBestPairType();
        int opponentPairValue = opponentBestPair?.value ?? 0;

        if (opponentBestPair != null)
        {
            Debug.Log($"对手最强可见对子：{opponentBestPair.typename}，权重：{opponentPairValue}");

            // 比较牌力：如果自己最大权重小于对手对子，则丢牌止损
            if (myMaxValue < opponentPairValue)
            {
                Debug.Log($"止损决策：自己最大权重({myMaxValue}) < 对手对子权重({opponentPairValue})，选择丢牌");
                return RobotConstants.ActionType.丢;
            }
            else
            {
                Debug.Log($"牌力优势：自己最大权重({myMaxValue}) >= 对手对子权重({opponentPairValue})，积极叫价");
                return GetAggressiveAction("牌力优势");
            }
        }
        else
        {
            Debug.Log("对手没有可见对子，根据自己牌力决策");
            return EarlyDecisionByCardValue(myBestCardType, myMaxValue, 4);
        }
    }

    /// <summary>
    /// 早期决策：根据牌力权重决定保守或积极
    /// </summary>
    /// <param name="cardType">当前最佳牌型</param>
    /// <param name="cardValue">当前最大权重</param>
    /// <param name="cardCount">手牌数量</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType EarlyDecisionByCardValue(XYCardType cardType, int cardValue, int cardCount)
    {
        Debug.Log($"早期决策：{cardCount}张牌，最佳权重{cardValue}");

        // 开局2张牌特殊处理
        if (cardCount == 2)
        {
            if (cardValue == 0)
            {
                Debug.Log("开局2张牌无特殊牌型，使用开局保守策略");
                return GetOpeningConservativeAction();
            }
            else if (cardValue >= 180)
            {
                Debug.Log($"开局2张牌有高价值牌型({cardValue})，积极叫价");
                return GetAggressiveAction("开局高价值");
            }
            else
            {
                Debug.Log($"开局2张牌有一般牌型({cardValue})，保守叫价");
                return GetConservativeAction("开局一般牌型");
            }
        }

        // 3-4张牌的正常判断
        int highValueThreshold = 180;  // 高价值阈值
        int mediumValueThreshold = 150; // 中等价值阈值

        if (cardValue >= highValueThreshold)
        {
            Debug.Log($"高价值牌型({cardValue} >= {highValueThreshold})，积极叫价");
            return GetAggressiveAction("高价值牌型");
        }
        else if (cardValue >= mediumValueThreshold)
        {
            Debug.Log($"中等价值牌型({cardValue} >= {mediumValueThreshold})，保守叫价");
            return GetConservativeAction("中等价值牌型");
        }
        else
        {
            Debug.Log($"低价值牌型({cardValue} < {mediumValueThreshold})，非常保守");
            return GetConservativeAction("低价值牌型");
        }
    }

    /// <summary>
    /// 开局保守策略：开局2张牌无特殊牌型时的超保守策略
    /// </summary>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetOpeningConservativeAction()
    {
        // 开局超保守：拖 > 跟 > 无脑策略，避免敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("使用 拖 (开局超保守)");
            return RobotConstants.ActionType.拖;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("使用 跟 (开局超保守)");
            return RobotConstants.ActionType.跟;
        }
        else
        {
            Debug.Log("开局无拖跟可选，使用无脑策略");
            return GetSimpleFapaiAction();
        }
    }

    /// <summary>
    /// 保守叫价策略
    /// </summary>
    /// <param name="reason">叫价原因</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetConservativeAction(string reason)
    {
        // 保守叫价：跟 > 拖 > 大(推荐位) > 休，避免敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log($"使用 跟 ({reason})");
            return RobotConstants.ActionType.跟;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log($"使用 拖 ({reason})");
            return RobotConstants.ActionType.拖;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log($"使用 大 (保守策略，跟拖不可用，{reason})");
            return RobotConstants.ActionType.大;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.休))
        {
            Debug.Log($"使用 休 (保守策略，跟拖大都不可用，{reason})");
            return RobotConstants.ActionType.休;
        }
        else
        {
            Debug.Log($"保守策略：所有保守选项都不可用，被迫使用无脑策略 ({reason})");
            return GetSimpleFapaiAction();
        }
    }

    /// <summary>
    /// 根据牌型名称决定操作
    /// </summary>
    /// <param name="cardTypeName">牌型名称</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType DecideActionByCardTypeName(string cardTypeName)
    {
        // 高价值牌型 - 敲
        if (GameDataHelper.IsHighValueCardType(cardTypeName))
        {
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
            {
                Debug.Log("使用 敲");
                return RobotConstants.ActionType.敲;
            }
            else
            {
                return GetSequentialAction();
            }
        }

        // 9点 - 跟
        if (cardTypeName == RobotConstants.CardTypes.NINE_POINTS)
        {
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
            {
                Debug.Log("使用 跟");
                return RobotConstants.ActionType.跟;
            }
            else
            {
                return GetSequentialAction();
            }
        }

        // 低点数（8点及以下）- 丢
        if (GameDataHelper.IsLowPointCardType(cardTypeName))
        {
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.丢))
            {
                Debug.Log("使用 丢");
                return RobotConstants.ActionType.丢;
            }
        }

        return GetFallbackAction();
    }

    /// <summary>
    /// 决定压注阶段的操作
    /// </summary>
    /// <param name="cardType">牌型信息</param>
    /// <returns>选择的操作类型</returns>
    public RobotConstants.ActionType DecideYazhuAction(XYCardType cardType)
    {
        var cards = GameDataHelper.CurrentHandCards;
        Debug.LogWarning($"压注阶段决策：手牌数量={cards?.Count ?? 0}");

        // 打印手牌信息用于调试
        if (cards != null && cards.Count > 0)
        {
            var cardInfo = string.Join(", ", cards.Select(c => $"{c.num}"));
            Debug.LogWarning($"当前手牌：{cardInfo}");
        }

        // 检查是否同时拥有Q和2（超级强牌）- 客户要求保留
        if (GameDataHelper.HasQueenAndTwo(cards))
        {
            Debug.Log("压注阶段检测到Q+2超级强牌组合，积极叫价！");
            return GetSmartBettingAction();
        }

        // 检查对手是否有红黑对，如果有则丢牌止损
        if (HasOpponentRedBlackPair())
        {
            Debug.Log("检测到对手有红黑对，选择丢牌止损");
            return RobotConstants.ActionType.丢;
        }

        // 无脑策略：优先选择跟，如果跟不可用则选择最小推荐位
        Debug.Log("使用无脑压注策略：优先跟牌，否则选择最小推荐位");
        return GetSimpleBettingAction();
    }

    /// <summary>
    /// 按优先级顺序选择操作
    /// </summary>
    /// <returns>可用的操作类型</returns>
    public RobotConstants.ActionType GetSequentialAction()
    {
        // 按优先级顺序：大 > 跟 > 敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("使用 大");
            return RobotConstants.ActionType.大;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("使用 跟");
            return RobotConstants.ActionType.跟;
        }
        else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("使用 敲");
            return RobotConstants.ActionType.敲;
        }

        return RobotConstants.ActionType.空;
    }

    /// <summary>
    /// 获取兜底操作 - 保留Q+2策略
    /// </summary>
    /// <returns>兜底操作类型</returns>
    public RobotConstants.ActionType GetFallbackAction()
    {
        var cards = GameDataHelper.CurrentHandCards;

        // 如果有Q+2组合，绝对不能丢牌 - 客户要求保留
        if (GameDataHelper.HasQueenAndTwo(cards))
        {
            Debug.LogWarning("兜底策略：有Q+2组合，避免丢牌，寻找其他操作");

            // 优先选择跟牌或敲牌，而不是丢牌
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
            {
                Debug.Log("兜底策略：使用 跟 (Q+2组合)");
                return RobotConstants.ActionType.跟;
            }
            else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
            {
                Debug.Log("兜底策略：使用 敲 (Q+2组合)");
                return RobotConstants.ActionType.敲;
            }
            else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.休))
            {
                Debug.Log("兜底策略：使用 休 (Q+2组合)");
                return RobotConstants.ActionType.休;
            }
        }

        // 普通情况的简化兜底策略：拖 > 跟 > 大 > 敲 > 丢
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("兜底策略：使用 拖（最小推荐位）");
            return RobotConstants.ActionType.拖;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("兜底策略：使用 跟");
            return RobotConstants.ActionType.跟;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("兜底策略：使用 大（推荐位）");
            return RobotConstants.ActionType.大;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("兜底策略：使用 敲（翻倍不了，没办法）");
            return RobotConstants.ActionType.敲;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.丢))
        {
            Debug.Log("兜底策略：使用 丢");
            return RobotConstants.ActionType.丢;
        }

        Debug.LogError("没有任何可用操作！");
        return RobotConstants.ActionType.空;
    }

    /// <summary>
    /// 获取紧急操作（用于即将超时的情况）- 保留Q+2策略
    /// </summary>
    /// <returns>紧急操作类型</returns>
    public RobotConstants.ActionType GetEmergencyAction()
    {
        var cards = GameDataHelper.CurrentHandCards;

        Debug.LogWarning("执行紧急操作决策");

        // 如果有Q+2组合，绝对不能休牌 - 客户要求保留
        if (GameDataHelper.HasQueenAndTwo(cards))
        {
            Debug.LogError("紧急情况：有Q+2组合，绝对不能休牌！");

            // 按优先级尝试所有积极操作
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
            {
                Debug.Log("紧急操作：使用 大 (Q+2组合)");
                return RobotConstants.ActionType.大;
            }
            else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
            {
                Debug.Log("紧急操作：使用 敲 (Q+2组合)");
                return RobotConstants.ActionType.敲;
            }
            else if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
            {
                Debug.Log("紧急操作：使用 跟 (Q+2组合)");
                return RobotConstants.ActionType.跟;
            }
        }

        // 普通情况下的简单策略：拖 > 跟 > 大 > 敲
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("紧急操作：使用 拖（最小推荐位）");
            return RobotConstants.ActionType.拖;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("紧急操作：使用 跟");
            return RobotConstants.ActionType.跟;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("紧急操作：使用 大（推荐位）");
            return RobotConstants.ActionType.大;
        }

        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("紧急操作：使用 敲（翻倍不了，没办法）");
            return RobotConstants.ActionType.敲;
        }

        // 最后的兜底策略
        return GetFallbackAction();
    }

    /// <summary>
    /// 执行选定的操作
    /// </summary>
    /// <param name="actionType">操作类型</param>
    /// <returns>是否成功执行</returns>
    public bool ExecuteAction(RobotConstants.ActionType actionType)
    {
        if (actionType == RobotConstants.ActionType.空)
        {
            return false;
        }

        bool success = _uiManager.ExecuteAction(actionType);

        if (success)
        {
            Debug.LogWarning(RobotConstants.DebugMessages.USING_ACTION + actionType);
        }

        return success;
    }

    /// <summary>
    /// 检查并执行牌型检查
    /// </summary>
    /// <param name="cards">手牌</param>
    /// <param name="dijiuwang">地九王规则</param>
    /// <returns>牌型信息</returns>
    public XYCardType CheckCardType(List<PaperCardStructrue> cards, bool dijiuwang)
    {
        if (cards == null || cards.Count < RobotConstants.GameConfig.SPLIT_CARDS_COUNT)
        {
            return null;
        }

        var cards1 = new List<PaperCardStructrue>
        {
            cards[0],
            cards[1]
        };

        return XYAllCardType.checkCardType(cards1, dijiuwang);
    }

    /// <summary>
    /// 检查是否应该对抗对手的对子而丢牌
    /// 当其他人有对子，而我方4张牌的最佳组合都无法超越对手对子时，选择丢牌
    /// </summary>
    /// <param name="myCards">我方4张手牌</param>
    /// <returns>是否应该丢牌</returns>
    private bool ShouldFoldAgainstOpponentPairs(List<PaperCardStructrue> myCards)
    {
        if (myCards == null || myCards.Count != 4) return false;

        // 获取对手最强的对子牌型
        var opponentBestPair = GetOpponentBestPairType();
        if (opponentBestPair == null)
        {
            Debug.Log("对手没有对子，继续正常叫价");
            return false; // 对手没有对子，不需要丢牌
        }

        Debug.Log($"检测到对手最强对子：{opponentBestPair.typename}，权重={opponentBestPair.value}");

        // 计算我方4张牌的最佳组合
        var myBestCardType = GetMyBestCardType(myCards);
        if (myBestCardType == null)
        {
            Debug.Log("我方没有有效牌型，选择丢牌");
            return true; // 我方没有有效牌型，丢牌
        }

        Debug.Log($"我方最佳牌型：{myBestCardType.typename}，权重={myBestCardType.value}");

        // 比较牌型权重，如果我方最佳牌型权重小于对手对子，则丢牌
        bool shouldFold = myBestCardType.value < opponentBestPair.value;

        if (shouldFold)
        {
            Debug.Log($"我方最佳牌型权重({myBestCardType.value}) < 对手对子权重({opponentBestPair.value})，选择丢牌");
        }
        else
        {
            Debug.Log($"我方最佳牌型权重({myBestCardType.value}) >= 对手对子权重({opponentBestPair.value})，继续叫价");
        }

        return shouldFold;
    }

    /// <summary>
    /// 智能叫价策略：根据是否首先叫价选择合适的倍数
    /// </summary>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetSmartBettingAction()
    {
        var roomData = GameDataHelper.RoomData;
        if (roomData == null) return GetSequentialAction();

        var difen = roomData.difen; // 底分
        var maxBet = roomData.operateMaxScore; // 当前最大叫价
        var myBetScore = GameDataHelper.CurrentUserInfo?.betScore ?? 0; // 我的已下注金额

        // 判断是否是首先叫价（当前最大叫价等于底分，说明还没人叫价）
        bool isFirstBet = (maxBet <= difen);

        if (isFirstBet)
        {
            Debug.Log("首先叫价：使用1-2倍底分策略");
            return GetFirstBettingAction(difen);
        }
        else
        {
            Debug.Log("跟进叫价：使用2-6倍底分策略");
            return GetFollowBettingAction(difen, maxBet);
        }
    }

    /// <summary>
    /// 首先叫价策略：1-2倍底分
    /// </summary>
    /// <param name="difen">底分</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetFirstBettingAction(int difen)
    {
        // 随机选择1-2倍底分
        int multiplier = UnityEngine.Random.Range(1, 3); // 1或2
        int targetAmount = difen * multiplier;

        Debug.Log($"首先叫价：选择{multiplier}倍底分 = {targetAmount}");

        // 优先使用推荐按钮，如果推荐按钮的金额合适
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            var tuijian1Amount = GetTuijianButtonAmount(1);
            if (tuijian1Amount > 0 && tuijian1Amount <= targetAmount * 1.5f) // 允许一定误差
            {
                Debug.Log($"使用推荐按钮1：{tuijian1Amount}");
                return RobotConstants.ActionType.拖;
            }
        }

        // 如果推荐按钮不合适，使用跟牌
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("使用跟牌");
            return RobotConstants.ActionType.跟;
        }

        // 兜底策略
        return GetSequentialAction();
    }

    /// <summary>
    /// 跟进叫价策略：基于当前最大叫价的2-4倍
    /// </summary>
    /// <param name="difen">底分</param>
    /// <param name="maxBet">当前最大叫价</param>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetFollowBettingAction(int difen, int maxBet)
    {
        // 基于当前最大叫价进行翻倍，随机选择2-4倍（适合翻倍玩法）
        float multiplier = UnityEngine.Random.Range(2f, 4.1f); // 2到4倍
        int targetAmount = Mathf.RoundToInt(maxBet * multiplier);

        // 确保至少比当前最大叫价多一些
        if (targetAmount <= maxBet)
        {
            targetAmount = maxBet * 2; // 至少翻倍
        }

        Debug.Log($"跟进叫价：基于当前最大叫价{maxBet} × {multiplier:F1} = {targetAmount}");

        // 寻找合适的推荐按钮
        for (int i = 1; i <= 3; i++)
        {
            if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
            {
                var tuijianAmount = GetTuijianButtonAmount(i);
                if (tuijianAmount > maxBet && tuijianAmount <= targetAmount * 1.3f) // 允许一定误差
                {
                    Debug.Log($"使用推荐按钮{i}：{tuijianAmount}（目标≤{targetAmount}）");
                    return RobotConstants.ActionType.拖;
                }
            }
        }

        // 如果推荐按钮都不合适，使用跟牌
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("使用跟牌");
            return RobotConstants.ActionType.跟;
        }

        // 兜底策略
        return GetSequentialAction();
    }

    /// <summary>
    /// 获取推荐按钮的金额
    /// </summary>
    /// <param name="buttonIndex">按钮索引（1-3）</param>
    /// <returns>推荐金额</returns>
    private int GetTuijianButtonAmount(int buttonIndex)
    {
        var optionPanel = _uiManager.OptionPanel;
        if (optionPanel == null) return 0;

        try
        {
            switch (buttonIndex)
            {
                case 1:
                    return int.Parse(optionPanel.tuijianT1.text);
                case 2:
                    return int.Parse(optionPanel.tuijianT2.text);
                case 3:
                    return int.Parse(optionPanel.tuijianT3.text);
                default:
                    return 0;
            }
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 获取对手最强的对子牌型
    /// </summary>
    /// <returns>对手最强对子牌型，如果没有对子则返回null</returns>
    private XYCardType GetOpponentBestPairType()
    {
        var roomData = GameDataHelper.RoomData;
        if (roomData?.players == null) return null;

        var myUserId = GameDataHelper.CurrentUserId;
        XYCardType bestPairType = null;

        foreach (var player in roomData.players)
        {
            // 跳过自己和已丢牌的玩家
            if (player.id == myUserId || player.operationType == 0 || player.ready != 1)
                continue;

            // 应该检查后两张牌（你能看到的）
            if (player.handCards != null && player.handCards.Count >= 2)
            {
                var cardType = XYAllCardType.checkCardType(new List<PaperCardStructrue>()
                {
                    player.handCards[2],
                     player.handCards[3]  // ✅ 正确：检查第3、4张牌
                }, GameDataHelper.IsDijiuwangEnabled);

                // 检查是否为对子牌型（通常对子牌型名称包含"对"字）
                if (cardType != null && IsPairCardType(cardType))
                {
                    if (bestPairType == null || cardType.value > bestPairType.value)
                    {
                        bestPairType = cardType;
                        Debug.Log($"发现玩家{player.id}的对子：{cardType.typename}，权重={cardType.value}");
                    }
                }
            }
        }

        return bestPairType;
    }

    /// <summary>
    /// 获取我方手牌的最佳牌型组合
    /// </summary>
    /// <param name="cards">我方手牌</param>
    /// <returns>最佳牌型</returns>
    private XYCardType GetMyBestCardType(List<PaperCardStructrue> cards)
    {
        if (cards == null || cards.Count < 2) return null;

        XYCardType bestType = null;

        // 遍历所有可能的两张牌组合
        for (int i = 0; i < cards.Count - 1; i++)
        {
            for (int j = i + 1; j < cards.Count; j++)
            {
                var cardType = XYAllCardType.checkCardType(new List<PaperCardStructrue>() {
                    cards[i], cards[j]
                }, GameDataHelper.IsDijiuwangEnabled);

                if (cardType != null)
                {
                    if (bestType == null || cardType.value > bestType.value)
                    {
                        bestType = cardType;
                    }
                }
            }
        }

        return bestType;
    }

    /// <summary>
    /// 判断牌型是否为对子类型
    /// </summary>
    /// <param name="cardType">牌型</param>
    /// <returns>是否为对子</returns>
    private bool IsPairCardType(XYCardType cardType)
    {
        if (cardType == null || string.IsNullOrEmpty(cardType.typename)) return false;

        // 根据牌型名称判断是否为对子
        // 通常对子牌型名称包含"对"字，或者是特定的对子牌型
        return cardType.typename.Contains("对") ||
               cardType.typename.Contains("红") ||
               cardType.typename.Contains("黑") ||
               (cardType.ID >= 2 && cardType.ID <= 16); // 修复：包含豹子9(ID=16)在内的对子范围(ID: 2-16)
    }

    /// <summary>
    /// 检查对手是否有红黑对
    /// </summary>
    /// <returns>是否有对手拥有红黑对</returns>
    private bool HasOpponentRedBlackPair()
    {
        var roomData = GameDataHelper.RoomData;
        if (roomData?.players == null) return false;

        var myUserId = GameDataHelper.CurrentUserId;

        foreach (var player in roomData.players)
        {
            // 跳过自己和已丢牌的玩家
            if (player.id == myUserId || player.operationType == 0 || player.ready != 1)
                continue;

            // 检查后两张牌（桌面显示的牌）
            if (player.handCards != null && player.handCards.Count >= 4)
            {
                var cardType = XYAllCardType.checkCardType(new List<PaperCardStructrue>()
                {
                    player.handCards[2],  // 第3张牌
                    player.handCards[3]   // 第4张牌
                }, GameDataHelper.IsDijiuwangEnabled);

                // 检查是否为红黑对
                if (cardType != null && IsRedBlackPair(cardType))
                {
                    Debug.Log($"发现玩家{player.id}有红黑对：{cardType.typename}");
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 判断是否为红黑对
    /// </summary>
    /// <param name="cardType">牌型</param>
    /// <returns>是否为红黑对</returns>
    private bool IsRedBlackPair(XYCardType cardType)
    {
        if (cardType == null) return false;

        // 根据XYAllCardType定义的红黑对ID范围判断
        // 红对子：ID 2-5, 10-12 (天牌,地牌,人牌,和牌,苕十,猫猫,膏药)
        // 黑对子：ID 6-9, 13-16 (梅十,板凳,长三,虎头,豹子5,豹子7,豹子8,豹子9)
        bool isRedPair = (cardType.ID >= 2 && cardType.ID <= 5) || (cardType.ID >= 10 && cardType.ID <= 12);
        bool isBlackPair = (cardType.ID >= 6 && cardType.ID <= 9) || (cardType.ID >= 13 && cardType.ID <= 16);

        if (isRedPair || isBlackPair)
        {
            Debug.Log($"检测到{(isRedPair ? "红" : "黑")}对子：{cardType.typename}，ID={cardType.ID}");
            return true;
        }

        return false;
    }

    /// <summary>
    /// 简单发牌策略：优先级 拖(最小推荐位) > 跟 > 大(推荐位) > 敲
    /// </summary>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetSimpleFapaiAction()
    {
        // 发牌阶段优先级：拖(最小推荐位) > 跟 > 大(推荐位) > 敲，永远不丢牌

        // 1. 优先拖（推荐位最小的那个）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("发牌无脑策略：使用 拖（最小推荐位）");
            return RobotConstants.ActionType.拖;
        }

        // 2. 然后跟
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("发牌无脑策略：使用 跟");
            return RobotConstants.ActionType.跟;
        }

        // 3. 接着大（也选择推荐位，也是拖）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("发牌无脑策略：使用 大（推荐位）");
            return RobotConstants.ActionType.大;
        }

        // 4. 敲（翻倍不了，没办法的选择）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("发牌无脑策略：使用 敲（翻倍不了，没办法）");
            return RobotConstants.ActionType.敲;
        }

        // 最后兜底，但绝不丢牌，选择休
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.休))
        {
            Debug.Log("发牌无脑策略：使用 休");
            return RobotConstants.ActionType.休;
        }

        // 实在没办法才丢牌
        Debug.LogError("发牌阶段：所有操作都不可用，被迫使用 丢");
        return RobotConstants.ActionType.丢;
    }

    /// <summary>
    /// 简单压注策略：优先级 拖(最小推荐位) > 跟 > 大(推荐位) > 敲 > 丢
    /// </summary>
    /// <returns>操作类型</returns>
    private RobotConstants.ActionType GetSimpleBettingAction()
    {
        // 压注阶段优先级：拖(最小推荐位) > 跟 > 大(推荐位) > 敲 > 丢

        // 1. 优先拖（推荐位最小的那个）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.拖))
        {
            Debug.Log("压注无脑策略：使用 拖（最小推荐位）");
            return RobotConstants.ActionType.拖;
        }

        // 2. 然后跟
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.跟))
        {
            Debug.Log("压注无脑策略：使用 跟");
            return RobotConstants.ActionType.跟;
        }

        // 3. 接着大（也选择推荐位，也是拖）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.大))
        {
            Debug.Log("压注无脑策略：使用 大（推荐位）");
            return RobotConstants.ActionType.大;
        }

        // 4. 敲（翻倍不了，没办法的选择）
        if (_uiManager.IsActionButtonAvailable(RobotConstants.ActionType.敲))
        {
            Debug.Log("压注无脑策略：使用 敲（翻倍不了，没办法）");
            return RobotConstants.ActionType.敲;
        }

        // 最后兜底选择丢牌
        Debug.Log("压注无脑策略：无其他选择，使用 丢");
        return RobotConstants.ActionType.丢;
    }
}
