﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>8.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_2020_3_48;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_STANDARD_2_0;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2020.3.48f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Script\Message\execute\XYDissolveClubRsp.cs" />
    <Compile Include="Assets\Script\Message\in\VerificationCodeRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\BroadcastOperationRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYLeaveUnionRspAbstract.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenProShortcuts.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubserchRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubinfoStruct.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYzongjiesuanPanel.cs" />
    <Compile Include="Assets\Script\Message\out\RealTimeRecordReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYNotifyPartnersokRsp.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubContributionUpdateReq.cs" />
    <Compile Include="Assets\Script\Message\in\XYmClubplayersRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\ControlBringInRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYBonusPointsRsp.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenTextMeshPro.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\CardSplitHelper.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindPlayerXinYulogRsp.cs" />
    <Compile Include="Assets\Script\Common\ScrollManager.cs" />
    <Compile Include="Assets\Script\Message\struct\SetListCheatCard_Structrue.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubDetailBossPanel.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\StrategyTest.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Album\AlbumApi.cs" />
    <Compile Include="Assets\Script\Message\execute\GetControlBringInDataRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\WebSocket.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindGameMinLogRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\tuijianPanel.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ShaderPropAnimator.cs" />
    <Compile Include="Assets\Script\GCloudVoice\GCloudVoiceEngine.cs" />
    <Compile Include="Assets\Script\Common\AsyncImageDownload.cs" />
    <Compile Include="Assets\Script\Common\Regularverification.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYPlayerGameEndPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\JoinClubPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindClubPartnerReq.cs" />
    <Compile Include="Assets\Script\Message\out\XYDissolveUnionReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpConnection.cs" />
    <Compile Include="Assets\Script\Message\execute\MeiriQiandao_XY.cs" />
    <Compile Include="Assets\Script\Managers\DataManager.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\paijuhuikanItem.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYbiaoqingNode.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ChunkedRequestStream.cs" />
    <Compile Include="Assets\Script\Message\out\XYDissolveClubReq.cs" />
    <Compile Include="Assets\Script\Message\in\ControlBringInRspAbstract.cs" />
    <Compile Include="Assets\Script\Common\GuidePostEvent.cs" />
    <Compile Include="Assets\Script\View\Xuaner\setDelete.cs" />
    <Compile Include="Assets\Script\Common\MyToggle.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MyUnionPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\EndPointListener.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\SpecialRuleStrategy.cs" />
    <Compile Include="Assets\Script\Message\struct\PaperCardStructrue.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SkewTextExample.cs" />
    <Compile Include="Assets\Script\Message\out\Logout.cs" />
    <Compile Include="Assets\tools\SocketIO\JSONObject\JSONObject.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\WarpTextExample.cs" />
    <Compile Include="Assets\Script\Message\out\VersionNumberReq.cs" />
    <Compile Include="Assets\Script\Message\execute\OtherLeaveRoom.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Clipboard\ClipboardApi.cs" />
    <Compile Include="Assets\Script\Message\execute\XYupdateUnionClubQuotaRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindGameAllLogRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubPartnerDrawMoneyReq.cs" />
    <Compile Include="Assets\Script\Message\out\HuoquLianmengEDuShouyi_QQ.cs" />
    <Compile Include="Assets\Script\Message\struct\XYunionClubeduLogStruct.cs" />
    <Compile Include="Assets\Script\View\login\Login.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SimpleScript.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\NetworkCredential.cs" />
    <Compile Include="Assets\Script\Message\struct\XYhalltableinfoStruct.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\CompressionMethod.cs" />
    <Compile Include="Assets\Script\Message\in\RealTimeRecordRspAbstract.cs" />
    <Compile Include="Assets\Script\animition\MovieClip.cs" />
    <Compile Include="Assets\Script\Message\in\LicensingRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYIndividualSettlementResultPanel.cs" />
    <Compile Include="Assets\Script\Message\in\ShenqingTuijianwei_XYAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\BroadcastCanOperationRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\BroadcastSetupboboAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListenerResponse.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubcreateRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYMenuPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\OperationRsp.cs" />
    <Compile Include="Assets\Script\Common\LoadSceneEnd.cs" />
    <Compile Include="Assets\Script\Common\playerBiaoQingItem.cs" />
    <Compile Include="Assets\2023\ChuangkouChuli.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYGameSetPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubUpdateUnionGongxianbiliRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\TabOutintegralChangeRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\CloseStatusCode.cs" />
    <Compile Include="Assets\Script\Robot\RobotConstants.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\paijuhuikanPageItem.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\Tuiguang_MB.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubPlayerxinyuLogStruct.cs" />
    <Compile Include="Assets\Script\Common\MyToggleManager.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_DigitValidator.cs" />
    <Compile Include="Assets\Script\Message\execute\VersionNumberRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubplayerStruct.cs" />
    <Compile Include="Assets\Script\Message\in\CreateRoomRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\ByteOrder.cs" />
    <Compile Include="Assets\tools\SoundButtonTool\SoundButtonTool.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Album\AlbumImplDummy.cs" />
    <Compile Include="Assets\Script\Message\out\ReadyReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\Julebu_Hehuoren_Chakandaili_YH.cs" />
    <Compile Include="Assets\Script\Message\struct\XYnotificationStruct.cs" />
    <Compile Include="Assets\Script\font\BitmapFontScaling.cs" />
    <Compile Include="Assets\CrossPlatformAPI\UI.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadcastSetupbobo.cs" />
    <Compile Include="Assets\Script\Message\in\BroadCastRuzuoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindPromotionInfoRsp.cs" />
    <Compile Include="Assets\Script\Common\PlaySprite.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Rsv.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Clipboard\ClipboardImplIos.cs" />
    <Compile Include="Assets\Script\Message\out\GameDelayedReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpHeaderInfo.cs" />
    <Compile Include="Assets\Script\Message\in\HuoquSuoyouTuijianwei_XYAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\HuoquSuoyouTuijianwei_QQ.cs" />
    <Compile Include="Assets\Script\Message\in\XYIntegralToDiamondRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\changtiaobiliui.cs" />
    <Compile Include="Assets\Script\Message\execute\ChatRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\FriendClubPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadcastAddOptionTimeRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ResponseStream.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MyClubItem.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\RequestStream.cs" />
    <Compile Include="Assets\Script\Message\in\PhoneRegisterRspAbstract.cs" />
    <Compile Include="Assets\Script\Common\UICustomTextGradient.cs" />
    <Compile Include="Assets\Script\Message\out\XYIntegralToDiamondReq.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\UI\UIImplAndroid.cs" />
    <Compile Include="Assets\Script\Message\execute\RoomInfoRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindClubPartnerRspAbstract.cs" />
    <Compile Include="Assets\Script\Robot\ActionDecisionSystem.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Share\ShareImplAndroid.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\LogData.cs" />
    <Compile Include="Assets\Script\Message\execute\RuzuoRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYgamemsgNode.cs" />
    <Compile Include="Assets\Script\Message\in\NoticeDifferentPlacesLoginRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYJiangCiExplainPanel.cs" />
    <Compile Include="Assets\Script\Robot\SmartBoboSelector.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MyPartnerItem.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadcastOperationRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYXiPaiRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\BonusOperateDetailPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYOptionheadNode.cs" />
    <Compile Include="Assets\Script\View\public\TransitionPanel.cs" />
    <Compile Include="Assets\Script\Message\struct\HuoquLianmengEDuBianhuaXX_JG.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\PaiJuRecordPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpBasicIdentity.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\Cookie.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYgpsPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYunionSerchReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\QueryStringCollection.cs" />
    <Compile Include="Assets\Script\Message\in\PlayerCuopaiNoticeRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\InputState.cs" />
    <Compile Include="Assets\tools\SocketIO\JSONObject\VectorTemplates.cs" />
    <Compile Include="Assets\Script\Message\execute\SetListCheatCardRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYCardSelectionPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\GamePlayerInfoRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Fin.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpDigestIdentity.cs" />
    <Compile Include="Assets\Script\Message\out\XYmailOperationReq.cs" />
    <Compile Include="Assets\Script\Message\out\ZuPaiReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\shopPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\GetJiFenXingYongEDuRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYmClubListRspAbstract.cs" />
    <Compile Include="Assets\Resources\Shader\animition\donghua1.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYpaixingtishiPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYPlayerPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\kefuPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubLeavePanel.cs" />
    <Compile Include="Assets\Script\Message\out\WechatLoginReq.cs" />
    <Compile Include="Assets\Script\Managers\logmanager.cs" />
    <Compile Include="Assets\Script\View\login\accountRegisterUI.cs" />
    <Compile Include="Assets\Script\Message\execute\ReadyRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\UnionDetailPanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\CSharpUtil.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindClubInfoReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\CookieCollection.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubPaiJuItem.cs" />
    <Compile Include="Assets\Script\Message\execute\XyfindIntegralLogRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\ControlBringInStruct.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmyUnionRsp.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark02.cs" />
    <Compile Include="Assets\Script\Message\in\XYGetClubInfoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\ControlBringInReq.cs" />
    <Compile Include="Assets\Script\WxLoginCore\AndroidLogin.cs" />
    <Compile Include="Assets\Script\Message\execute\PlayerOperateCuopaiRsp.cs" />
    <Compile Include="Assets\Script\Message\out\XYmClubplayersReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\AuthenticationSchemes.cs" />
    <Compile Include="Assets\Script\Message\out\BankerGameStartReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\EditDataPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\AuthenticationResponse.cs" />
    <Compile Include="Assets\Script\Message\execute\LoginRsp.cs" />
    <Compile Include="Assets\Script\Message\out\GetJiFenXingYongEDuReq.cs" />
    <Compile Include="Assets\Script\Message\execute\GetTableinfoRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindClubPartnerRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Logger.cs" />
    <Compile Include="Assets\Script\Common\RLRotate.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpVersion.cs" />
    <Compile Include="Assets\Script\Message\struct\PlayerInfo.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListener.cs" />
    <Compile Include="Assets\Script\Message\MySoc.cs" />
    <Compile Include="Assets\Script\Message\in\MyGameWinRateDataRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindUnionClubeduLogRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\HuoquSuoyouTuijianwei_XY.cs" />
    <Compile Include="Assets\Script\Message\execute\PlayerCuopaiNoticeRsp.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Clipboard\ClipboardImplDummy.cs" />
    <Compile Include="Assets\Script\Message\in\XYunionSerchRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubjoinReq.cs" />
    <Compile Include="Assets\Script\Common\MyConfiger.cs" />
    <Compile Include="Assets\Script\Message\execute\CreateRoomRsp.cs" />
    <Compile Include="Assets\Script\Managers\SceneManager.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshSpawner.cs" />
    <Compile Include="Assets\Script\Message\execute\XYunionCreateRsp.cs" />
    <Compile Include="Assets\Script\Message\out\EnterRoomReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\CreateUnionPanel.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubPartnerCuttingRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\HandshakeBase.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubPlayerUpdateBonusShareReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\OperatePanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Attributes\NotNullAttribute.cs" />
    <Compile Include="Assets\Script\Message\in\XYmClubupdateRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindDividendOperationRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\BackgroundSwitchExample.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYjiatelingNOde.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindPlayerXinYulogRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\Scripts\SocketIO\SocketIOException.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\JoinUnionPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYwenziNode.cs" />
    <Compile Include="Assets\Script\Message\out\XYLeaveTheClubReq.cs" />
    <Compile Include="Assets\Script\Message\in\GameDelayedRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubIncomeStatisticsLogPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\HeartbeatRsp.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\UI\UIImplDummy.cs" />
    <Compile Include="Assets\Script\Message\in\OtherLeaveRoomAbstract.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubContributionLogStruct.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYwenziUI.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01_UGUI.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubIncomeStatisticsPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ListenerAsyncResult.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\AuthenticationChallenge.cs" />
    <Compile Include="Assets\Script\Message\execute\SummaryCalculationRsp.cs" />
    <Compile Include="Assets\Script\Common\MySoundManager.cs" />
    <Compile Include="Assets\Script\Message\out\HeartbeatReq.cs" />
    <Compile Include="Assets\Script\Message\in\XYhalltableListRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindJuniorPlayerRsp.cs" />
    <Compile Include="Assets\Script\Message\out\XYunionCreateReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\PaiJuRecordItem.cs" />
    <Compile Include="Assets\Script\Message\out\AddOptionTimeReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYbecomeAPartnerRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYXiPaiRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubMsgItem.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_A.cs" />
    <Compile Include="Assets\Script\Message\out\XYmClubListReq.cs" />
    <Compile Include="Assets\Script\Message\in\UpdateuserRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\resetPasswordReq.cs" />
    <Compile Include="Assets\Script\Message\in\XyfindIntegralLogRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\VisitorLoginReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\WebSockets\TcpListenerWebSocketContext.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Mask.cs" />
    <Compile Include="Assets\Script\Message\in\NoticeCuopaiEndRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\GetJiFenXingYongEDuRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYshishizhanjiPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\NoticeCuopaiEndRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\SettlementStructrue.cs" />
    <Compile Include="Assets\Script\Managers\WindowsManager.cs" />
    <Compile Include="Assets\Script\Common\MyRectTran.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\WebSockets\HttpListenerWebSocketContext.cs" />
    <Compile Include="Assets\Script\Message\in\BroadcastZuPaiRspAbstract.cs" />
    <Compile Include="Assets\Script\GCloudVoice\GCloudVoiceNotify.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HintFramePanel.cs" />
    <Compile Include="Assets\Script\Message\in\ReadyNoticeAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\GameReviewRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindPlayersContributionRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\additem.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\guanyuwomenPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYZupaiPanel.cs" />
    <Compile Include="Assets\Script\Message\in\HuoquLianmengEDuBianhuaXX_XYAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XyfindIntegralLogReq.cs" />
    <Compile Include="Assets\tools\SocketIO\Scripts\SocketIO\Packet.cs" />
    <Compile Include="Assets\Script\Message\execute\XYInformDataAlterRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListenerException.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindPlayersContributionReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYIntegralToDiamondRsp.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\ICardSplitStrategy.cs" />
    <Compile Include="Assets\Script\Message\out\ChatStringReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpStreamAsyncResult.cs" />
    <Compile Include="Assets\Script\Message\in\RuzuoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\OperationReq.cs" />
    <Compile Include="Assets\Script\Message\out\RoomMasterOperateReq.cs" />
    <Compile Include="Assets\Script\Message\out\SetLongitudeLatitudeReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark03.cs" />
    <Compile Include="Assets\Script\Message\struct\IndividualSettlementRecordStructrue.cs" />
    <Compile Include="Assets\Script\View\Xuaner\gonggao\gonggaoPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYGetClubInfoRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindClubTheFundSubsidiaryRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\joinroomPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYmClubSetplayerReq.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindClubInfoRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\WebHeaderCollection.cs" />
    <Compile Include="Assets\Script\Message\out\XYtopUpIntegralReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\daSlider.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\AuthenticationBase.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\BonusPointsPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MyPartnerPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmClubupdateRsp.cs" />
    <Compile Include="Assets\Script\Message\out\HuoquLianmengEDuShouyiXX_QQ.cs" />
    <Compile Include="Assets\Script\View\Xuaner\yuyindonghua.cs" />
    <Compile Include="Assets\Script\View\Xuaner\CreateRoom\mSlider.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\UI\UIApi.cs" />
    <Compile Include="Assets\Script\Robot\JiqirenCanshu.cs" />
    <Compile Include="Assets\Script\Message\out\XYLeaveUnionReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\WebSocketException.cs" />
    <Compile Include="Assets\Script\Message\execute\ChatStringRsp.cs" />
    <Compile Include="Assets\Julebu_Lianmeng_EduBianhua_MB.cs" />
    <Compile Include="Assets\Script\errlog.cs" />
    <Compile Include="Assets\Script\Message\execute\MyGameWinRateDataRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\RoomMasterOperateRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYDissolveUnionRsp.cs" />
    <Compile Include="Assets\Script\Robot\RobotUIManager.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYwenziliaotianPanel.cs" />
    <Compile Include="Assets\Script\Common\ScrollRectManager.cs" />
    <Compile Include="Assets\Script\Common\EventTriggerListener.cs" />
    <Compile Include="Assets\tools\SocketIO\JSONObject\JSONTemplates.cs" />
    <Compile Include="Assets\Script\Message\execute\EnterRoomRsp.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeA.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\dataItem.cs" />
    <Compile Include="Assets\Script\Message\in\XYupdateUnionClubQuotaRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\BankerGameStartRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\CreateRoom\createRoomPanel.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\MaxValueStrategy.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListenerPrefixCollection.cs" />
    <Compile Include="Assets\Script\Message\out\XyUpGReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\FundOperateDetailItem.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindJuniorPlayerReq.cs" />
    <Compile Include="Assets\2023\ShijianjianChuli.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberItem.cs" />
    <Compile Include="Assets\Script\Message\execute\XYunionSerchRsp.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Share\ShareApi.cs" />
    <Compile Include="Assets\Script\Message\out\MeiriQiandao_QQ.cs" />
    <Compile Include="Assets\Julebu_Lianmeng_EduShouyi_BQ.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HuoquLianmengEDuShouyiXX_RQ.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark04.cs" />
    <Compile Include="Assets\Script\Message\execute\UpdateuserRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\NoticeDifferentPlacesLoginRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYLeaveTheClubRspAbstract.cs" />
    <Compile Include="Assets\Julebu_Lianmeng_EduBianhua_BQ.cs" />
    <Compile Include="Assets\tools\SocketIO\Scripts\SocketIO\SocketIOEvent.cs" />
    <Compile Include="Assets\Script\Message\execute\ReadyNotice.cs" />
    <Compile Include="Assets\Script\Message\out\XYapplyJoinUnionReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubPartnerDrawMoneyRsp.cs" />
    <Compile Include="Assets\Script\Common\MyGride.cs" />
    <Compile Include="Assets\Script\Message\in\OtherEnterRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubcreateRsp.cs" />
    <Compile Include="Assets\Script\Message\in\UserInfoAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindInformationRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HistoryPaiJuRecordPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberCreditLimitPanel3.cs" />
    <Compile Include="Assets\Script\Message\out\XYmyUnionReq.cs" />
    <Compile Include="Assets\Script\Message\execute\HuoquLianmengEDuShouyiXX_XY.cs" />
    <Compile Include="Assets\Script\Message\out\UpdateuserReq.cs" />
    <Compile Include="Assets\Script\Message\execute\SetupboboRsp.cs" />
    <Compile Include="Assets\Script\Message\in\GameEndRspAbstract.cs" />
    <Compile Include="Assets\Script\Common\ExtensionsTransform.cs" />
    <Compile Include="Assets\Script\Message\in\XYapplyJoinUnionRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadCastRuzuoRsp.cs" />
    <Compile Include="Assets\Script\Common\ObjScale.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\ErrorEventArgs.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\SearchClubPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYyuNode.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYzongjiesuanNode.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\hallpanel.cs" />
    <Compile Include="Assets\Script\Common\PageView.cs" />
    <Compile Include="Assets\Script\Message\execute\ShenqingTuijianwei_XY.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\AssemblyInfo.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_FrameRateCounter.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadcastZuPaiRsp.cs" />
    <Compile Include="Assets\Script\Message\out\GetTableinfoReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubMsgPanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Share.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics2D.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenAnimation.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindGameMinLogRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListenerRequest.cs" />
    <Compile Include="Assets\Script\View\loading\loadingPanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Album.cs" />
    <Compile Include="Assets\Script\Common\GetScreenTexture.cs" />
    <Compile Include="Assets\Script\Robot\GameDataHelper.cs" />
    <Compile Include="Assets\Demigiant\DOTweenPro\DOTweenTk2d.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYbiaoqingPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYLeaveTheClubRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ReadBufferState.cs" />
    <Compile Include="Assets\Script\Message\out\IndividualSettlementRecordReq.cs" />
    <Compile Include="Assets\Script\View\login\ChangePasswordPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindPersonalSettlementLogReq.cs" />
    <Compile Include="Assets\Script\Common\GetGPS.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ListenerPrefix.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubUpdateUnionGongxianbiliReq.cs" />
    <Compile Include="Assets\Script\Common\PaoWuXianMove.cs" />
    <Compile Include="Assets\Script\Message\execute\SettlementRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\HandshakeRequest.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubjoinRspAbstract.cs" />
    <Compile Include="Assets\Script\Robot\JiqirenZhuozi.cs" />
    <Compile Include="Assets\Script\Message\execute\LeaveRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubPremiumOplogStruct.cs" />
    <Compile Include="Assets\Script\Message\in\SetupboboRspAbstract.cs" />
    <Compile Include="Assets\2023\ChuangkouRongqi.cs" />
    <Compile Include="Assets\Script\Message\struct\GameStartStructrue.cs" />
    <Compile Include="Assets\Script\Message\execute\ZuPaiRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubFundPanel.cs" />
    <Compile Include="Assets\Script\Message\struct\XYgameMinlogStruct.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberCreditLimitPanel2.cs" />
    <Compile Include="Assets\Script\Message\in\XYbecomeAPartnerRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubContributionUpdateRsp.cs" />
    <Compile Include="Assets\Script\Message\out\GameReviewReq.cs" />
    <Compile Include="Assets\Script\Message\struct\XYunioninfoStruct.cs" />
    <Compile Include="Assets\Script\Message\struct\GameReviewStructrue.cs" />
    <Compile Include="Assets\Script\Message\out\ChatReq.cs" />
    <Compile Include="Assets\Script\Message\out\VerificationCodeReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYtopUpIntegralRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYDissolveClubRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindGameAllLogReq.cs" />
    <Compile Include="Assets\Script\Message\in\XYunionCreateRspAbstract.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Album\AlbumImplAndroid.cs" />
    <Compile Include="Assets\Script\Managers\GvoiceManager.cs" />
    <Compile Include="Assets\Script\Message\out\SetListCheatCardReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ChatController.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\SingelClubPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\Security\SslStream.cs" />
    <Compile Include="Assets\Script\GCloudVoice\GCloudVoice.cs" />
    <Compile Include="Assets\Script\Message\out\ShenqingTuijianwei_QQ.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubjoinRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubPlayerUpdateBonusShareRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYOptionPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpHeaderType.cs" />
    <Compile Include="Assets\Script\Message\in\GetTableinfoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\GameDelayedRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\CloseEventArgs.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexJitter.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshProFloatingText.cs" />
    <Compile Include="Assets\Script\Message\struct\LicensingStructure.cs" />
    <Compile Include="Assets\Script\Message\struct\ClubIncomeStatisticsStruct.cs" />
    <Compile Include="Assets\Script\Message\out\GetWatchWarPlayerInfoReq.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\MessageEventArgs.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYSitdownRotatePanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Opcode.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYliuzuoPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MyUnionItem.cs" />
    <Compile Include="Assets\Script\Common\UnityPing.cs" />
    <Compile Include="Assets\Script\Message\execute\GetClubIncomeStatisticsLogRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XYhalltableListRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\ClubIncomeStatisticsLogStruct.cs" />
    <Compile Include="Assets\Script\WxLoginCore\IOSLogin.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\UnionPanel.cs" />
    <Compile Include="Assets\Script\Managers\GameManager.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\SetPanel.cs" />
    <Compile Include="Assets\Script\Message\in\resetPasswordRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\ChatRspAbstract.cs" />
    <Compile Include="Assets\Script\Common\SlidingInspection.cs" />
    <Compile Include="Assets\Script\Message\in\SummaryCalculationRspAbstract.cs" />
    <Compile Include="Assets\tools\SkipUnityLogo.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYDiamondExchangePanel.cs" />
    <Compile Include="Assets\Script\Message\out\GamePlayerInfoReq.cs" />
    <Compile Include="Assets\Script\Message\out\ZhanghaoLoginReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventHandler.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindDividendOperationReq.cs" />
    <Compile Include="Assets\Script\Message\in\MeiriQiandao_XYAbstract.cs" />
    <Compile Include="Assets\Script\Common\yuyinClickLisner.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\ChunkStream.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmClubListRsp.cs" />
    <Compile Include="Assets\Script\Message\in\VersionNumberRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\PlayerBonusPanel.cs" />
    <Compile Include="Assets\Script\Managers\transManager.cs" />
    <Compile Include="Assets\Script\Message\in\PlayerOperateCuopaiRspAbstract.cs" />
    <Compile Include="Assets\Script\Common\Signal.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\SearchUnionPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\IntegralLogPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberCreditLimitItem.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\AndroidUtil.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYChatPlayingPanel.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubPartnerDrawMoneyRspAbstract.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Share\ShareImplDummy.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindInformationReq.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\CardSplitStrategyManager.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\EndPointManager.cs" />
    <Compile Include="Assets\Script\Message\execute\XYapplyJoinUnionRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\guzhangNode.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYRoominfo.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpListenerContext.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeB.cs" />
    <Compile Include="Assets\Script\Robot\JiqirenDenglu.cs" />
    <Compile Include="Assets\Script\Message\out\XYunionupdateReq.cs" />
    <Compile Include="Assets\Script\Message\execute\resetPasswordRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\VerificationCodeRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYgamemsgPanel.cs" />
    <Compile Include="Assets\Script\Message\out\HuoquLianmengEDuBianhuaXX_QQ.cs" />
    <Compile Include="Assets\Script\Message\in\NoticeCuopaiRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\SingelClubPanels\PlayPlayerItem.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYXiPaiPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindClubInfoRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\XyRefreshNotificationRsp.cs" />
    <Compile Include="Assets\Script\Message\in\GetControlBringInDataRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\WebSocketState.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_B.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberSetPanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\UI\AlertParams.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\Julebu_Hehuoren_Chakandaili_MB.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubcreateReq.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindPlayerXinYulogReq.cs" />
    <Compile Include="Assets\Script\Message\execute\GetClubIncomeStatisticsRsp.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\EnvMapAnimator.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubContributionUpdateRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubCreditLimitItem.cs" />
    <Compile Include="Assets\Script\Message\struct\Tuijianwei_JG.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModulePhysics.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\BonusPointsItem.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_UiFrameRateCounter.cs" />
    <Compile Include="Assets\Script\Message\execute\GameEndRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberCreditLimitPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYSetboboPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\WebSocketFrame.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYshishizhanjiItem.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\CookieException.cs" />
    <Compile Include="Assets\Script\Message\in\RoomMasterOperateRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubUpdateUnionGongxianbiliRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\GetWatchWarPlayerInfoRsp.cs" />
    <Compile Include="Assets\Script\Message\out\BrightCardReq.cs" />
    <Compile Include="Assets\Script\Common\TextPic.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\MeiriqiandaoPanel.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindUnionClubeduLogRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindPersonalSettlementLogRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindDividendOperationRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\RoomInfoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\MyGameWinRateDataReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYgamespeakPanel.cs" />
    <Compile Include="Assets\Script\Message\out\RuzuoReq.cs" />
    <Compile Include="Assets\Script\Message\in\XyRefreshNotificationRspAbstract.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUnityVersion.cs" />
    <Compile Include="Assets\Script\Message\in\BroadcastAddOptionTimeRspAbstract.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleSprite.cs" />
    <Compile Include="Assets\Script\Message\out\XYGetClubInfoReq.cs" />
    <Compile Include="Assets\Script\Managers\ResourceManager.cs" />
    <Compile Include="Assets\Script\Message\in\XYtopUpIntegralRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\LicensingRsp.cs" />
    <Compile Include="Assets\Script\Message\in\IndividualSettlementRecordRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XYhalltableListReq.cs" />
    <Compile Include="Assets\Script\Robot\GameStateHandler.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\Clublevel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYShayuNode.cs" />
    <Compile Include="Assets\Script\Message\in\LeaveRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\TabOutintegralChangeRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HistoryPaiJuPanel.cs" />
    <Compile Include="Assets\Script\Message\in\OperationRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\IntegralLogItem.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubPlayerUpdateBonusShareRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\CreateClubPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberCreditLimitItem2.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\WebSockets\WebSocketContext.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Clipboard.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberlogItem.cs" />
    <Compile Include="Assets\Script\Message\execute\GameStartRsp.cs" />
    <Compile Include="Assets\Script\Message\out\XYXiPaiReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmClubSetplayerRsp.cs" />
    <Compile Include="Assets\Script\Message\execute\HuoquLianmengEDuBianhuaXX_XY.cs" />
    <Compile Include="Assets\Script\Message\in\XYNotifyPartnersokRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\struct\WatchWarPlayerInfoStructrue.cs" />
    <Compile Include="Assets\Script\Message\in\SetListCheatCardRsp_XYAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\Scripts\SocketIO\SocketIOComponent.cs" />
    <Compile Include="Assets\Script\Robot\JiqirenDating.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUtils.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubInviteFriendPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\OtherEnterRsp.cs" />
    <Compile Include="Assets\Script\Message\out\getuserinfo.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindPromotionInfoRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\BonusOperateDetailItem.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Clipboard\ClipboardImplAndroid.cs" />
    <Compile Include="Assets\Script\Message\execute\XYclubPartnerCuttingRsp.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\CameraController.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleUI.cs" />
    <Compile Include="Assets\Script\Common\WindowBase.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\addPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindGameAllLogRsp.cs" />
    <Compile Include="Assets\Script\Robot\Strategy\DefaultStrategy.cs" />
    <Compile Include="Assets\Script\Message\in\ZuPaiRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYunionupdateRsp.cs" />
    <Compile Include="Assets\Script\Message\out\CreateRoomReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\dataPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\WebSocketStream.cs" />
    <Compile Include="Assets\Script\Message\out\XYupdateUnionClubQuotaReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYTablePanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\LineState.cs" />
    <Compile Include="Assets\Script\Message\in\ChatStringRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\XYLeaveUnionRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\HandshakeResponse.cs" />
    <Compile Include="Assets\Script\Message\execute\BrightCardRsp.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubDetailPanel.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubJiJinLogStruct.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindPromotionInfoReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\LowerLevelPlayerContributionItem.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubBasePanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\mePanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\EditClubDataPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\ClubCreditLimitPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYHintPanel.cs" />
    <Compile Include="Assets\Resources\Shader\liuguang.cs" />
    <Compile Include="Assets\Script\Message\struct\XYgameAlllogStruct.cs" />
    <Compile Include="Assets\Script\Message\out\PhoneRegisterReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmClubplayersRsp.cs" />
    <Compile Include="Assets\Script\Common\DontDestroyOnLoad.cs" />
    <Compile Include="Assets\Script\Message\in\GetWatchWarPlayerInfoRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\GetClubIncomeStatisticsLogRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\HeartbeatRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HistoryPaiJuRecordItem.cs" />
    <Compile Include="Assets\Script\Message\in\XYDissolveUnionRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\BroadcastCanOperationRsp.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\DropdownSample.cs" />
    <Compile Include="Assets\Script\Message\out\ChaxunLianmengJulebuRizhi_QQ.cs" />
    <Compile Include="Assets\Script\Message\in\AddOptionTimeRspAbstract.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_PhoneNumberValidator.cs" />
    <Compile Include="Assets\Script\Message\in\XYBonusPointsRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\GameStartRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\GetClubIncomeStatisticsReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYPlayerzupaicardsNode.cs" />
    <Compile Include="Assets\Tuiguangwei_YG.cs" />
    <Compile Include="Assets\Script\WxLoginCore\WxLoginBase.cs" />
    <Compile Include="Assets\Script\Message\out\BankerKickReq.cs" />
    <Compile Include="Assets\Script\View\login\PhoneRegisterPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpStatusCode.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubJuniorMemberStruct.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMPro_InstructionOverlay.cs" />
    <Compile Include="Assets\Script\Message\execute\HuoquLianmengEDuShouyi_XY.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexColorCycler.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindInformationRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\HttpUtility.cs" />
    <Compile Include="Assets\Julebu_Lianmeng_EduBianhua_YH.cs" />
    <Compile Include="Assets\Script\View\login\loginpanel.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Share\ShareImplIos.cs" />
    <Compile Include="Assets\Script\Message\struct\HuoquLianmengEDuShouyiXX_JG.cs" />
    <Compile Include="Assets\Julebu_Lianmeng_EduShouyi_YH.cs" />
    <Compile Include="Assets\Script\Message\in\HuoquLianmengEDuShouyi_XYAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\GetClubIncomeStatisticsLogReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TeleType.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_ExampleScript_01.cs" />
    <Compile Include="Assets\Script\View\public\myScrollbarVertical.cs" />
    <Compile Include="Assets\Script\Message\in\LoginRspAbstract.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Ext.cs" />
    <Compile Include="Assets\Script\Message\struct\awards.cs" />
    <Compile Include="Assets\Script\Message\out\WeiChatReqLoginReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextConsoleSimulator.cs" />
    <Compile Include="Assets\Demigiant\DOTween\Modules\DOTweenModuleAudio.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\PlayerBonusItem.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindGameMinLogReq.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindClubTheFundSubsidiaryReq.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubPartnerCuttingReq.cs" />
    <Compile Include="Assets\Script\Message\struct\XyIntegralLogStruct.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexZoom.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYCuopaiPanel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYdaojuNode.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ObjectSpin.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYweiguanItem.cs" />
    <Compile Include="Assets\Script\Message\execute\PhoneRegisterRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\RealTimeRecordStructrue.cs" />
    <Compile Include="Assets\Script\Message\in\GetClubIncomeStatisticsRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\XYfindUnionClubeduLogReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\FundOperateDetailPanel.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindJuniorPlayerRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\SettlementRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\XYInformDataAlterRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\SingelClubPanels\DeskItem.cs" />
    <Compile Include="Assets\Script\Message\execute\BankerGameStartRsp.cs" />
    <Compile Include="Assets\2023\ChuangkouJichu.cs" />
    <Compile Include="Assets\Script\Managers\SoundManager.cs" />
    <Compile Include="Assets\Resources\Shader\shuiliu.cs" />
    <Compile Include="Assets\Script\Message\execute\NoticeCuopaiRsp.cs" />
    <Compile Include="Assets\Script\Message\in\HuoquLianmengEDuShouyiXX_XYAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\hall\NoticePanel.cs" />
    <Compile Include="Assets\Script\Message\in\XYmyUnionRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYIndividualSettlementResultItem.cs" />
    <Compile Include="Assets\Script\Message\execute\IndividualSettlementRecordRsp.cs" />
    <Compile Include="Assets\Script\Message\struct\XYPersonalSettlementLogStruct.cs" />
    <Compile Include="Assets\CrossPlatformAPI\CrossPlatformAPI.cs" />
    <Compile Include="Assets\Script\Message\out\XYclubserchReq.cs" />
    <Compile Include="Assets\Script\Message\in\BrightCardRspAbstract.cs" />
    <Compile Include="Assets\Script\Robot\CompileTest.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\MemberPanel.cs" />
    <Compile Include="Assets\Script\GCloudVoice\GCloudVoiceExtension.cs" />
    <Compile Include="Assets\Script\Message\out\leaveReq.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindPlayersContributionRspAbstract.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextInfoDebugTool.cs" />
    <Compile Include="Assets\Script\Message\in\XYmailOperationRspAbstract.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\Album\AlbumImplIos.cs" />
    <Compile Include="Assets\Script\Message\out\XYleaveClubReq.cs" />
    <Compile Include="Assets\Script\Message\execute\XYfindPersonalSettlementLogRsp.cs" />
    <Compile Include="Assets\Script\Message\in\XYmClubSetplayerRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\in\GamePlayerInfoRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\paijuhuikanPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYmClubupdateReq.cs" />
    <Compile Include="Assets\Script\Message\out\GetControlBringInDataReq.cs" />
    <Compile Include="Assets\Script\Message\struct\HuoquLianmengEDuShouyi_JG.cs" />
    <Compile Include="Assets\Script\Message\execute\UserInfo.cs" />
    <Compile Include="Assets\Script\Common\Singleton.cs" />
    <Compile Include="Assets\Script\Message\in\XYfindClubTheFundSubsidiaryRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYEmjioPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\PayloadData.cs" />
    <Compile Include="Assets\CrossPlatformAPI\Implementations\UI\UIImplIos.cs" />
    <Compile Include="Assets\StartPanel.cs" />
    <Compile Include="Assets\Script\Message\system\systemmessage.cs" />
    <Compile Include="Assets\Script\GCloudVoice\GCloudVoiceErrno.cs" />
    <Compile Include="Assets\Script\Message\out\SetupboboReq.cs" />
    <Compile Include="Assets\Script\Message\in\EnterRoomRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\out\PlayerOperateCuopaiReq.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01.cs" />
    <Compile Include="Assets\Script\Message\struct\XYclubPartnerStruct.cs" />
    <Compile Include="Assets\Script\View\public\Tishi2Manager.cs" />
    <Compile Include="Assets\Script\Message\in\XYclubserchRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYcuopaimoveNode.cs" />
    <Compile Include="Assets\Script\Message\in\ReadyRspAbstract.cs" />
    <Compile Include="Assets\Script\Message\execute\GameReviewRsp.cs" />
    <Compile Include="Assets\Script\Message\out\PlayerCuopaiNoticeReq.cs" />
    <Compile Include="Assets\Script\Message\execute\RealTimeRecordRsp.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\Chunk.cs" />
    <Compile Include="Assets\Script\Common\GetBullety.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\LowerLevelPlayerContributionPanel.cs" />
    <Compile Include="Assets\Script\Message\out\XYbecomeAPartnerReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYremoveroomPanel.cs" />
    <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventCheck.cs" />
    <Compile Include="Assets\Script\Message\execute\XYmailOperationRsp.cs" />
    <Compile Include="Assets\MemberJifenPanel.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\Net\InputChunkState.cs" />
    <Compile Include="Assets\Script\Message\out\XYBonusPointsReq.cs" />
    <Compile Include="Assets\Script\View\Xuaner\BatteryorTime.cs" />
    <Compile Include="Assets\tools\SocketIO\WebsocketSharp\LogLevel.cs" />
    <Compile Include="Assets\Script\View\Xuaner\Club\HistoryPaiJuItem.cs" />
    <Compile Include="Assets\Script\Message\in\XYunionupdateRspAbstract.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYCardNode.cs" />
    <Compile Include="Assets\Script\Common\InfinityGridLayoutGroup.cs" />
    <Compile Include="Assets\Script\View\Xuaner\game\XYgametipsPanel.cs" />
    <Compile Include="Assets\Script\Message\execute\AddOptionTimeRsp.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Oswald-Bold - OFL.txt" />
    <None Include="Assets\Resources\Shader\mask.shader" />
    <None Include="Assets\Resources\Shader\shanguang.shader" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Anton OFL.txt" />
    <None Include="Assets\tools\SocketIO\JSONObject\readme.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\Resources\Shader\huidu.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\Demigiant\readme_DOTweenPro.txt" />
    <None Include="Assets\Demigiant\DOTween\DOTween.XML" />
    <None Include="Assets\tools\erweima\zxing.unity.xml" />
    <None Include="Assets\Demigiant\DemiLib\Core\DemiLib.dll" />
    <None Include="Assets\Script\转换日志20230618020736.txt" />
    <None Include="Assets\Resources\Shader\outline.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\tools\SocketIO\Readme.txt" />
    <None Include="Assets\tools\erweima\zxing.unity.dll" />
    <None Include="Assets\Resources\Shader\UI-Wave2.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\Demigiant\DOTween\DOTween.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\Resources\Shader\hall\HeadUnionClub.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\Resources\Shader\UI-Reflection.shader" />
    <None Include="Assets\StreamingAssets\log.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\Resources\Shader\animition\donghua.shader" />
    <None Include="Assets\Demigiant\DOTweenPro\DOTweenPro.dll" />
    <None Include="Assets\Demigiant\DOTweenPro\readme.txt" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\Resources\Shader\mohu.shader" />
    <None Include="Assets\Demigiant\DemiLib\Core\DemiLib.xml" />
    <None Include="Assets\Resources\Shader\UISeqFrameAni.shader" />
    <None Include="Assets\Resources\Shader\UI-Wave1.shader" />
    <None Include="Assets\Demigiant\DOTweenPro\DOTweenPro.XML" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\Resources\Shader\animition\donghua 1.shader" />
    <None Include="Assets\Demigiant\DOTween\readme.txt" />
    <None Include="Assets\Resources\Shader\fire.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\Resources\Shader\UI-Wave3.shader" />
    <None Include="Assets\tools\json\LitJson.dll" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\Resources\Shader\mask 1.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\Resources\Shader\liuguang.shader" />
    <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Bangers - OFL.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsNativeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsNativeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PackageManagerUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.PackageManagerUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIServiceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIServiceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>Assets\tools\json\LitJson.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>Assets\tools\erweima\zxing.unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\ref\2.0.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2020.3.48f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="NativeGallery.Editor.csproj" />
    <ProjectReference Include="NativeGallery.Runtime.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
