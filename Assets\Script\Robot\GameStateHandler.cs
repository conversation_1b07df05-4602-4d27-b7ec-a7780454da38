using System.Collections;
using System.Linq;
using UnityEngine;
using SocketIO;

/// <summary>
/// 游戏状态处理器
/// 处理不同游戏状态下的机器人行为
/// </summary>
public class GameStateHandler
{
    private readonly RobotUIManager _uiManager;
    private readonly ActionDecisionSystem _decisionSystem;
    private readonly CardSplitStrategyManager _strategyManager;
    private readonly SmartBoboSelector _boboSelector;

    // 上分冷却时间控制
    private float _lastScoringTime = 0f;
    private const float SCORING_COOLDOWN = 30f; // 30秒冷却时间

    public GameStateHandler(RobotUIManager uiManager, ActionDecisionSystem decisionSystem)
    {
        _uiManager = uiManager;
        _decisionSystem = decisionSystem;
        _strategyManager = new CardSplitStrategyManager();
        _boboSelector = new SmartBoboSelector(uiManager);

        // 监听房间总结算消息（房间真正结束时）
        MySoc.socket.On("SummaryCalculationRsp", OnRoomSettlement);

        // 监听游戏结束消息，立即检查上分
        MySoc.socket.On("GameEndRsp", OnGameEnd);

        // 监听游戏开始消息，重置叫价次数
        MySoc.socket.On("GameStartRsp", OnGameStart);
    }

    /// <summary>
    /// 处理组牌状态
    /// </summary>
    public IEnumerator HandleZupaiState()
    {
        yield return new WaitForSeconds(Random.Range(
            RobotConstants.Timing.RANDOM_ACTION_MIN, 
            RobotConstants.Timing.RANDOM_ACTION_MAX));

        if (!_uiManager.IsZupaiPanelAvailable()) yield break;

        var cards = GameDataHelper.CurrentHandCardsArray;
        if (cards.Length != RobotConstants.GameConfig.HAND_CARDS_COUNT)
        {
            yield break;
        }

        // 使用策略系统进行分牌
        var (tmpI, tmpJ) = _strategyManager.GetBestSplit(cards, GameDataHelper.IsDijiuwangEnabled);

        Debug.LogWarning($"{RobotConstants.DebugMessages.STRATEGY_SELECTION} {tmpI} 和 {tmpJ}");

        // 验证选择的牌型（用于调试）
        var selectedCardType = XYAllCardType.checkCardType(new System.Collections.Generic.List<PaperCardStructrue>() {
            cards[tmpI],
            cards[tmpJ]
        }, GameDataHelper.IsDijiuwangEnabled);

        if (selectedCardType != null)
        {
            Debug.LogWarning(RobotConstants.DebugMessages.FINAL_CARD_TYPE + selectedCardType.typename);
        }

        var zupai = _uiManager.ZupaiPanel;
        zupai.clickBottomCard(tmpI);

        yield return new WaitForSeconds(RobotConstants.Timing.CARD_CLICK_DELAY);

        // 因为前面点击掉了一张牌，所以要少1
        zupai.clickBottomCard(tmpJ - 1);

        yield return new WaitForSeconds(Random.Range(
            RobotConstants.Timing.RANDOM_ACTION_MIN, 
            RobotConstants.Timing.RANDOM_ACTION_MAX));

        zupai.clickFenpai();

        yield return new WaitForSeconds(Random.Range(
            RobotConstants.Timing.RANDOM_ACTION_MIN, 
            RobotConstants.Timing.RANDOM_ACTION_MAX));
    }

    /// <summary>
    /// 处理发牌状态（逻辑版本）
    /// </summary>
    public IEnumerator HandleFapaiState()
    {
        yield return new WaitForSeconds(Random.Range(
            RobotConstants.Timing.RANDOM_ACTION_MIN,
            RobotConstants.Timing.RANDOM_ACTION_MAX));

        // 检查是否轮到机器人操作
        if (!GameDataHelper.IsMyTurn)
        {
            Debug.Log($"不是机器人操作轮次，当前操作玩家ID：{GameDataHelper.CurrentOperateUserId}，机器人ID：{GameDataHelper.CurrentUserId}");
            yield break;
        }

        if (!_uiManager.IsOptionPanelAvailable()) yield break;

        var cards = GameDataHelper.CurrentHandCards;
        if (cards.Count < RobotConstants.GameConfig.SPLIT_CARDS_COUNT) yield break;

        var cardType = _decisionSystem.CheckCardType(cards, GameDataHelper.IsDijiuwangEnabled);
        var actionType = _decisionSystem.DecideFapaiAction(cardType, cards);

        if (actionType == RobotConstants.ActionType.空)
        {
            actionType = _decisionSystem.GetFallbackAction();
        }

        // 操作前等待0.5秒
        Debug.Log($"准备执行发牌操作：{actionType}，等待2.5秒...");
        yield return new WaitForSeconds(0.5f);

        _decisionSystem.ExecuteAction(actionType);
    }

    /// <summary>
    /// 处理压注状态
    /// </summary>
    public IEnumerator HandleYazhuState()
    {
        yield return new WaitForSeconds(Random.Range(
            RobotConstants.Timing.RANDOM_ACTION_MIN,
            RobotConstants.Timing.RANDOM_ACTION_MAX));

        // 检查是否轮到机器人操作
        if (!GameDataHelper.IsMyTurn)
        {
            Debug.Log($"不是机器人操作轮次，当前操作玩家ID：{GameDataHelper.CurrentOperateUserId}，机器人ID：{GameDataHelper.CurrentUserId}");
            yield break;
        }

        if (!_uiManager.IsOptionPanelAvailable()) yield break;

        var cards = GameDataHelper.CurrentHandCards;
        if (cards.Count < RobotConstants.GameConfig.SPLIT_CARDS_COUNT) yield break;

        var cardType = _decisionSystem.CheckCardType(cards, GameDataHelper.IsDijiuwangEnabled);
        var actionType = _decisionSystem.DecideYazhuAction(cardType);

        if (actionType == RobotConstants.ActionType.空)
        {
            actionType = _decisionSystem.GetFallbackAction();
        }

        // 操作前等待0.5秒
        Debug.Log($"准备执行压注操作：{actionType}，等待0.5秒...");
        yield return new WaitForSeconds(0.5f);

        _decisionSystem.ExecuteAction(actionType);
    }

    /// <summary>
    /// 处理坐下相关操作
    /// </summary>
    public IEnumerator HandleSeatOperations()
    {
        yield return new WaitForEndOfFrame();

        // 判定当前是否坐下
        if (!GameDataHelper.IsUserSeated)
        {
            yield return HandleSitDown();
        }
        else
        {
            yield return HandleReturnToSeat();
        }

        yield return new WaitForSeconds(RobotConstants.Timing.MAIN_LOOP_INTERVAL);
    }

    /// <summary>
    /// 处理坐下操作
    /// </summary>
    private IEnumerator HandleSitDown()
    {
        var emptySeats = _uiManager.GetEmptySeats();

        if (emptySeats != null && emptySeats.Count > 0)
        {
            var randomIndex = Random.Range(0, emptySeats.Count);
            var selectedSeat = emptySeats[randomIndex];
            selectedSeat.clickKongwei();

            yield return new WaitForSeconds(RobotConstants.Timing.SEAT_OPERATION_DELAY);
        }
        else
        {
            Debug.LogError(RobotConstants.DebugMessages.NO_EMPTY_SEAT);
        }
    }

    /// <summary>
    /// 处理回到座位操作
    /// </summary>
    private IEnumerator HandleReturnToSeat()
    {
        _uiManager.RefreshPlayerPanel();

        if (_uiManager.CanReturnToSeat())
        {
            // 点击回到座位
            _uiManager.PlayerPanel.clickliuzuoHuizhuo();
        }

        yield return null;
    }

    /// <summary>
    /// 处理自动上分操作
    /// 只在真正需要上分时才执行，避免频繁上分
    /// </summary>
    public IEnumerator HandleAutoScoring()
    {
        yield return new WaitForEndOfFrame();

        // 检查冷却时间
        if (Time.time - _lastScoringTime < SCORING_COOLDOWN)
        {
            yield break; // 还在冷却中，不执行上分
        }

        // 检查是否需要且可以上分
        if (!GameDataHelper.CanScore)
        {
            // 调试信息：为什么不需要上分
            var debugBobo = GameDataHelper.CurrentUserBobo;
            var isSeated = GameDataHelper.IsUserSeated;
            var hasIntegral = GameDataHelper.HasSufficientIntegral;
            var needsScoring = GameDataHelper.NeedsScoring;

            if (!isSeated)
            {
                // 没坐下，不输出日志避免刷屏
            }
            else if (!needsScoring)
            {
                // 波波充足，不需要上分
                Debug.Log($"波波充足，无需上分：当前波波={debugBobo}");
            }
            else if (!hasIntegral)
            {
                Debug.LogWarning("积分不足，无法上分");
            }

            yield break; // 不需要上分，直接退出
        }

        // 检查设置波波面板是否可用
        if (!_uiManager.IsSetboboPanelAvailable())
        {
            yield break; // 面板不可用，无法上分
        }

        var currentBobo = GameDataHelper.CurrentUserBobo;
        var roomMinScore = GameDataHelper.RoomData?.dairumin ?? 100;

        Debug.Log($"检测到需要上分：当前波波={currentBobo}, 房间最小带入={roomMinScore}");

        var setboboPanel = _uiManager.SetboboPanel;

        // 使用智能波波选择器
        var selectedIndex = _boboSelector.SelectBoboIndex();
        setboboPanel.OnToggle(selectedIndex);

        var recommendedValue = _boboSelector.GetRecommendedBoboValue();
        Debug.Log($"智能波波选择：索引={selectedIndex}, 推荐值={recommendedValue}");

        yield return new WaitForSeconds(RobotConstants.Timing.SCORE_OPERATION_DELAY);
        setboboPanel.clickConfirm();

        // 更新上分时间，开始冷却
        _lastScoringTime = Time.time;

        Debug.Log($"上分完成，开始{SCORING_COOLDOWN}秒冷却时间");
        yield return new WaitForSeconds(RobotConstants.Timing.SCORE_OPERATION_DELAY);
    }

    /// <summary>
    /// 处理房间总结算消息，自动返回大厅
    /// </summary>
    private void OnRoomSettlement(SocketIOEvent e)
    {
        Debug.Log("检测到房间总结算，准备返回大厅");

        // 延迟3秒后返回大厅，给结算界面一些显示时间
        var robotComponent = GameObject.FindObjectOfType<JiqirenZhuozi>();
        if (robotComponent != null)
        {
            robotComponent.StartCoroutine(ReturnToHallAfterDelay());
        }
    }

    /// <summary>
    /// 延迟返回大厅
    /// </summary>
    private IEnumerator ReturnToHallAfterDelay()
    {
        // 随机等待5-10秒，避免给服务器造成压力
        float delayTime = UnityEngine.Random.Range(5f, 10f);
        Debug.Log($"等待{delayTime:F1}秒后返回大厅");
        yield return new WaitForSeconds(delayTime);

        Debug.Log("开始返回大厅");

        // 直接使用场景切换返回大厅
        game.SceneManager.instance.OpenScene("Scene/", "Hall");
        Debug.Log("已切换到大厅场景");
    }

    /// <summary>
    /// 处理游戏结束消息，立即检查上分需求
    /// </summary>
    private void OnGameEnd(SocketIOEvent e)
    {
        Debug.Log("检测到游戏结束，立即检查上分需求");

        // 重置上分冷却时间，允许立即上分
        _lastScoringTime = 0f;

        // 启动立即上分检查
        var robotComponent = GameObject.FindObjectOfType<JiqirenZhuozi>();
        if (robotComponent != null)
        {
            robotComponent.StartCoroutine(ImmediateScoreCheck());
        }
    }

    /// <summary>
    /// 立即检查上分需求（游戏结束后）
    /// </summary>
    private IEnumerator ImmediateScoreCheck()
    {
        // 等待一帧，确保游戏状态更新
        yield return new WaitForEndOfFrame();

        Debug.Log("游戏结束后立即检查上分需求");

        // 检查是否需要上分
        if (GameDataHelper.IsUserSeated && GameDataHelper.HasSufficientIntegral)
        {
            var currentBobo = GameDataHelper.CurrentUserBobo;
            var roomStartMinScore = GameDataHelper.RoomData?.startMinScore ?? 100;

            Debug.Log($"上分检查：当前波波={currentBobo}, 房间最小分={roomStartMinScore}");

            if (currentBobo < roomStartMinScore)
            {
                Debug.Log("检测到需要立即上分，执行上分操作");
                yield return HandleAutoScoring();
            }
            else
            {
                Debug.Log("波波充足，无需上分");
            }
        }
        else
        {
            Debug.Log("未坐下或积分不足，无法上分");
        }
    }

    /// <summary>
    /// 处理游戏开始消息，重置叫价次数
    /// </summary>
    private void OnGameStart(SocketIOEvent e)
    {
        Debug.Log("检测到游戏开始，重置叫价次数");
        _decisionSystem.ResetBettingCount();
    }
}
