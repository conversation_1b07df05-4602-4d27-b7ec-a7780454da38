﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class XYOptionPanel : WindowBase
{
    public Button tuijianBtn1, tuijianBtn2, tuijianBtn3;
    public Button genBtn, xiuBtn, diuBtn, qiaoBtn, daBtn, nodaBtn;
    public Toggle zidongxiuTog, zidongxiudiuTog;
    public Animator anim;
    public TMPro.TextMeshProUGUI tuijianT1, tuijianT2, tuijianT3;
    public TMPro.TextMeshProUGUI qiaoT, genT;
    public daSlider mdaslider;
    public Button yanshiBtn;

    public void initzidongdiuxiu()
    {
        zidongxiudiuTog.SetIsOnWithoutNotify(false);
        zidongxiuTog.SetIsOnWithoutNotify(false);
    }
    public void init()
    {
        anim.Play("XYOptionAnim");
        tuijianBtn1.gameObject.SetActive(false);
        tuijianBtn2.gameObject.SetActive(false);
        tuijianBtn3.gameObject.SetActive(false);
        genBtn.gameObject.SetActive(false);
        xiuBtn.gameObject.SetActive(false);
        diuBtn.gameObject.SetActive(false);
        qiaoBtn.gameObject.SetActive(false);
        daBtn.gameObject.SetActive(false);
        nodaBtn.gameObject.SetActive(false);
        zidongxiudiuTog.gameObject.SetActive(false);
        zidongxiuTog.gameObject.SetActive(false);
        mdaslider.gameObject.SetActive(false);
        yanshiBtn.gameObject.SetActive(false);
        if (game.DataManager.instance.roominfo.roomdata.operateUid == game.DataManager.instance.m_UserInfo.id)
        {
            //setTuijianText();
            setDaTuijianText();

            // 修正：直接使用 keYiJiaojia 判断，并检查是否有可用的推荐按钮
            if (game.DataManager.instance.roominfo.roomdata.keYiJiaojia == true)
            {
                // 可以叫价：显示推荐按钮
                tuijianBtn1.gameObject.SetActive(true);
                tuijianBtn2.gameObject.SetActive(true);
                tuijianBtn3.gameObject.SetActive(true);

                // 检查是否有任何推荐按钮可用（分数够翻倍）
                var wanjiaZongfen = game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore;
                var maxfen = game.DataManager.instance.roominfo.roomdata.operateMaxScore;
                bool hasValidRecommendation = (maxfen * 2 <= wanjiaZongfen);

                if (hasValidRecommendation)
                {
                    // 有可用的推荐按钮，显示大牌滑动条
                    if (game.DataManager.instance.roominfo.roomdata.isxiu != 1)
                    {
                        genT.text = game.DataManager.instance.roominfo.roomdata.operateMaxScore.ToString();
                    }
                    mdaslider.init();
                    daBtn.gameObject.SetActive(true);
                }
                else
                {
                    // 分数不够翻倍，不显示大牌滑动条
                    daBtn.gameObject.SetActive(false);
                    mdaslider.gameObject.SetActive(false);
                }
            }
            else
            {
                // 不能叫价时，隐藏大牌按钮和滑动条
                daBtn.gameObject.SetActive(false);
                mdaslider.gameObject.SetActive(false);
            }

            // 判断是否是第一个操作的玩家（当前玩家押注等于底分 且 最大压分也等于底分）
            bool isFirstPlayer = (game.DataManager.instance.roominfo.userInfo.betScore <= game.DataManager.instance.roominfo.roomdata.difen) &&
                                (game.DataManager.instance.roominfo.roomdata.operateMaxScore <= game.DataManager.instance.roominfo.roomdata.difen);

            // 敲牌按钮：只有在可以叫价时才显示敲牌按钮
            // 没有竞争的循环叫价时，不能敲牌，只能跟和丢
            if (!isFirstPlayer &&
                game.DataManager.instance.roominfo.roomdata.keYiJiaojia == true &&
                game.DataManager.instance.roominfo.userInfo.bobo > 0)
            {
                qiaoBtn.gameObject.SetActive(true);
            }
            else
            {
                qiaoBtn.gameObject.SetActive(false);
            }

            // 跟牌金额计算（无论是否第一个玩家都需要计算）
            if (game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore > game.DataManager.instance.roominfo.roomdata.operateMaxScore)
            {
                genT.text = game.DataManager.instance.roominfo.roomdata.operateMaxScore.ToString();
            }
            else
            {
                genT.text = (game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore).ToString();
            }
            qiaoT.text = (game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore).ToString();

            // 跟牌/休牌按钮显示逻辑：完全依赖后端的isxiu标志
            if (game.DataManager.instance.roominfo.roomdata.isxiu == 1)
            {
                // 可以休牌：显示休牌按钮，隐藏跟牌按钮
                xiuBtn.gameObject.SetActive(true);
                genBtn.gameObject.SetActive(false);
            }
            else
            {
                // 不能休牌：显示跟牌按钮，隐藏休牌按钮
                xiuBtn.gameObject.SetActive(false);

                // 第一个玩家在不能休牌时不显示跟牌按钮（没有人可以跟）
                if (isFirstPlayer)
                {
                    genBtn.gameObject.SetActive(false);
                }
                else
                {
                    genBtn.gameObject.SetActive(true);
                }
            }

            // 丢牌按钮：总是显示
            diuBtn.gameObject.SetActive(true);
            if (zidongxiuTog.isOn)
            {
                if (xiuBtn.gameObject.activeSelf)
                {
                    clickXiu();
                    game.logmanager.Log("自动点休1");
                }
            }
            if (zidongxiudiuTog.isOn)
            {
                if (xiuBtn.gameObject.activeSelf)
                {
                    clickXiu();
                    game.logmanager.Log("自动点休2");
                }
                else
                {
                    clickDiu();
                    game.logmanager.Log("自动点丢");
                }
            }
            yanshiBtn.gameObject.SetActive(true);
        }
        else
        {
            zidongxiudiuTog.gameObject.SetActive(true);
            zidongxiuTog.gameObject.SetActive(true);

        }
    }
    public void setDaTuijianText()
    {
        var wanjiaYafen = game.DataManager.instance.roominfo.userInfo.betScore;
        var wanjiaShengyu = game.DataManager.instance.roominfo.userInfo.bobo;
        var maxfen = game.DataManager.instance.roominfo.roomdata.operateMaxScore;
        var difen = game.DataManager.instance.roominfo.roomdata.difen;
        var mangguochi = game.DataManager.instance.roominfo.roomdata.mangguochi;
        var pichi = game.DataManager.instance.roominfo.roomdata.pichi;
        var wanjiaZongfen = wanjiaShengyu + wanjiaYafen;

        if (game.DataManager.instance.roominfo.roomdata.keYiJiaojia == false)
        {
            tuijianBtn1.interactable = false;
            tuijianBtn2.interactable = false;
            tuijianBtn3.interactable = false;

            tuijianBtn1.gameObject.SetActive(false);
            tuijianBtn2.gameObject.SetActive(false);
            tuijianBtn3.gameObject.SetActive(false);
            return;
        }

        if (wanjiaZongfen > difen && maxfen > 0)
        {
            tuijianT1.text = (maxfen * 2).ToString();
            tuijianT2.text = (maxfen * 4).ToString();
            tuijianT3.text = (maxfen * 8).ToString();

            tuijianBtn1.interactable = true;
            tuijianBtn2.interactable = true;
            tuijianBtn3.interactable = true;

            if (maxfen * 2 > wanjiaZongfen)
            {
                tuijianBtn1.interactable = false;
            }
            if (maxfen * 4 > wanjiaZongfen)
            {
                tuijianBtn2.interactable = false;
            }
            if (maxfen * 8 > wanjiaZongfen)
            {
                tuijianBtn3.interactable = false;
            }
        }
        else if (mangguochi > 0)
        {
            tuijianT1.text = (mangguochi * 1).ToString();
            tuijianT2.text = (mangguochi * 2).ToString();
            tuijianT3.text = (mangguochi * 4).ToString();
            tuijianBtn1.interactable = true;
            tuijianBtn2.interactable = true;
            tuijianBtn3.interactable = true;
            if (mangguochi * 1 >= wanjiaZongfen)
            {
                tuijianBtn1.interactable = false;
            }
            if (mangguochi * 2 > wanjiaZongfen)
            {
                tuijianBtn2.interactable = false;
            }
            if (mangguochi * 4 > wanjiaZongfen)
            {
                tuijianBtn3.interactable = false;
            }
        }
        else
        {
            tuijianT1.text = (pichi * 2).ToString();
            tuijianT2.text = (pichi * 4).ToString();
            tuijianT3.text = (pichi * 8).ToString();
            tuijianBtn1.interactable = true;
            tuijianBtn2.interactable = true;
            tuijianBtn3.interactable = true;
            if (pichi * 2 >= wanjiaZongfen)
            {
                tuijianBtn1.interactable = false;
            }
            if (pichi * 4 > wanjiaZongfen)
            {
                tuijianBtn2.interactable = false;
            }
            if (pichi * 8 > wanjiaZongfen)
            {
                tuijianBtn3.interactable = false;
            }
        }
    }
    //public void setTuijianText()
    //{
    //    if (game.DataManager.instance.roominfo.roomdata.operateMaxScore > game.DataManager.instance.roominfo.roomdata.difen)
    //    {
    //        tuijianT1.text = (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 2).ToString();
    //        tuijianT2.text = (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 4).ToString();
    //        tuijianT3.text = (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 8).ToString();
    //        tuijianBtn1.interactable = true;
    //        tuijianBtn2.interactable = true;
    //        tuijianBtn3.interactable = true;
    //        if (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 2 >= game.DataManager.instance.roominfo.userInfo.bobo+game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn1.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 4 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn2.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.operateMaxScore * 8 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn3.interactable = false;
    //        }
    //    }
    //    else if (game.DataManager.instance.roominfo.roomdata.mangguochi > 0)
    //    {
    //        tuijianT1.text = (game.DataManager.instance.roominfo.roomdata.mangguochi * 1).ToString();
    //        tuijianT2.text = (game.DataManager.instance.roominfo.roomdata.mangguochi * 2).ToString();
    //        tuijianT3.text = (game.DataManager.instance.roominfo.roomdata.mangguochi * 4).ToString();
    //        tuijianBtn1.interactable = true;
    //        tuijianBtn2.interactable = true;
    //        tuijianBtn3.interactable = true;
    //        if (game.DataManager.instance.roominfo.roomdata.mangguochi * 1 >= game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn1.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.mangguochi * 2 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn2.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.mangguochi * 4 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn3.interactable = false;
    //        }
    //    }
    //    else
    //    {
    //        tuijianT1.text = (game.DataManager.instance.roominfo.roomdata.pichi * 2).ToString();
    //        tuijianT2.text = (game.DataManager.instance.roominfo.roomdata.pichi * 4).ToString();
    //        tuijianT3.text = (game.DataManager.instance.roominfo.roomdata.pichi * 8).ToString();
    //        tuijianBtn1.interactable = true;
    //        tuijianBtn2.interactable = true;
    //        tuijianBtn3.interactable = true;
    //        if (game.DataManager.instance.roominfo.roomdata.pichi * 2 >= game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn1.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.pichi * 4 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn2.interactable = false;
    //        }
    //        if (game.DataManager.instance.roominfo.roomdata.pichi * 8 > game.DataManager.instance.roominfo.userInfo.bobo + game.DataManager.instance.roominfo.userInfo.betScore)
    //        {
    //            tuijianBtn3.interactable = false;
    //        }
    //    }
    //}


    public void clickTuijian(int index)
    {
        //ZKTODO 按钮音效
        OperationReq req = new OperationReq(PacketType.OperationReq);
        req.operation = 3;
        switch (index)
        {
            case 1:
                req.chip = int.Parse(tuijianT1.text) - game.DataManager.instance.roominfo.userInfo.betScore;
                break;
            case 2:
                req.chip = int.Parse(tuijianT2.text) - game.DataManager.instance.roominfo.userInfo.betScore;
                break;
            case 3:
                req.chip = int.Parse(tuijianT3.text) - game.DataManager.instance.roominfo.userInfo.betScore;
                break;
        }
        MySoc.socket.Emit(req);
    }


    public void clickDiu()
    {
        //ZKTODO 按钮音效 
        OperationReq req = new OperationReq(PacketType.OperationReq);
        req.operation = 0;
        req.chip = 0;
        MySoc.socket.Emit(req);
    }
    public void clickXiu()
    {
        //ZKTODO 按钮音效 
        OperationReq req = new OperationReq(PacketType.OperationReq);
        req.operation = 1;
        req.chip = 0;
        MySoc.socket.Emit(req);
    }
    public void clickQiao()
    {
        //ZKTODO 按钮音效
        OperationReq req = new OperationReq(PacketType.OperationReq);
        req.operation = 2;
        req.chip = game.DataManager.instance.roominfo.userInfo.bobo;
        MySoc.socket.Emit(req);
    }
    public void clickDa()
    {
        //ZKTODO 按钮音效 
    }
    public void clickGen()
    {
        //ZKTODO 按钮音效
        int genScore = game.DataManager.instance.roominfo.roomdata.operateMaxScore - game.DataManager.instance.roominfo.userInfo.betScore;
        if (genScore >= game.DataManager.instance.roominfo.userInfo.bobo)
        {
            OperationReq req = new OperationReq(PacketType.OperationReq);
            req.operation = 2;
            req.chip = game.DataManager.instance.roominfo.userInfo.bobo;
            MySoc.socket.Emit(req);
        }
        else
        {
            OperationReq req = new OperationReq(PacketType.OperationReq);
            req.operation = 5;
            req.chip = genScore;
            MySoc.socket.Emit(req);
        }
    }
    public void clickYanshi()
    {
        //ZKTODO 按钮音效 
        GameDelayedReq req = new GameDelayedReq(PacketType.GameDelayedReq);
        MySoc.socket.Emit(req);
    }

    //机器人随机大
    public void suijiDa()
    {
        mdaslider.suijiDa();
    }

    //机器人智能大（支持2-6倍叫价）
    public void SmartDa()
    {
        mdaslider.SmartDa();
    }

}
