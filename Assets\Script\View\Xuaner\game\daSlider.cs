using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class daSlider : MonoBehaviour
{
    public TextMeshProUGUI topT, moveT;
    public mSlider slider;
    private int maxValue = 0;
    public GameObject tuijianbtn2,dabtn;
    private int minValue = 0;

    public void Start()
    {
    }
    public void init()
    {
        maxValue = game.DataManager.instance.roominfo.userInfo.betScore + game.DataManager.instance.roominfo.userInfo.bobo;
        dabtn.SetActive(true);
        GetComponent<CanvasGroup>().alpha = 0;
        gameObject.SetActive(true);
        topT.text = maxValue.ToString();
        minValue = game.DataManager.instance.roominfo.roomdata.operateMaxScore * 2;
        if (minValue <= 0)
        {
            minValue = game.DataManager.instance.roominfo.roomdata.mangguochi;
        }
        if (minValue <= 0)
        {
            minValue = game.DataManager.instance.roominfo.roomdata.pichi*2;
        }
        slider.init(onChange,(game.DataManager.instance.roominfo.userInfo.bobo-(minValue - game.DataManager.instance.roominfo.userInfo.betScore))/game.DataManager.instance.roominfo.roomdata.difen);
        moveT.text = "0";
        tuijianbtn2.transform.SetAsLastSibling();
    }
    

    public void onChange(int index)
    {
        int value = index == 0? 0: minValue + (index*game.DataManager.instance.roominfo.roomdata.difen);
        moveT.text = value.ToString();
        if (int.Parse(moveT.text) == maxValue)
        {
            game.SoundManager.instance.PlaySound("slider_top");
        }
        else
        {
            game.SoundManager.instance.PlaySound("slider");
        }
    }
   
    public void onDown()
    {
        GetComponent<CanvasGroup>().alpha = 1;
        tuijianbtn2.transform.SetAsFirstSibling();
        dabtn.SetActive(false);
    }
    public void onEnd()
    {
        dabtn.SetActive(true);
        GetComponent<CanvasGroup>().alpha = 0;
        tuijianbtn2.transform.SetAsLastSibling();
        int nowValue = int.Parse(moveT.text);
        if (nowValue == 0)
        {
            return;
        }
        if (nowValue == maxValue)
        {
            OperationReq req = new OperationReq(PacketType.OperationReq);
            req.chip = nowValue - game.DataManager.instance.roominfo.userInfo.betScore;
            req.operation = 2;
            MySoc.socket.Emit(req);
        }
        else
        {
            OperationReq req = new OperationReq(PacketType.OperationReq);
            req.chip = nowValue - game.DataManager.instance.roominfo.userInfo.betScore;
            req.operation = 3;
            MySoc.socket.Emit(req);
        }
    }

    public void suijiDa()
    {
        var tmp = Random.Range(minValue, maxValue);
        moveT.text = tmp.ToString();

        onEnd();
    }

    /// <summary>
    /// 智能大操作（支持2-6倍叫价）
    /// </summary>
    public void SmartDa()
    {
        // 获取当前底分
        int difen = game.DataManager.instance.roominfo.roomdata.difen;
        int currentBet = game.DataManager.instance.roominfo.userInfo.betScore;

        // 计算2-6倍的叫价范围
        int minMultiple = 2; // 最少2倍
        int maxMultiple = 6; // 最多6倍

        // 随机选择倍数（2-6倍）
        int selectedMultiple = Random.Range(minMultiple, maxMultiple + 1);

        // 计算目标叫价金额
        int targetAmount = currentBet + (selectedMultiple * difen);

        // 确保不超过最大可用金额
        if (targetAmount > maxValue)
        {
            targetAmount = maxValue;
            Debug.Log($"智能大：目标金额超过最大值，调整为最大值 {maxValue}");
        }

        // 确保不低于最小叫价
        if (targetAmount < minValue)
        {
            targetAmount = minValue;
            Debug.Log($"智能大：目标金额低于最小值，调整为最小值 {minValue}");
        }

        Debug.Log($"智能大：选择{selectedMultiple}倍叫价，目标金额={targetAmount}（底分={difen}，当前押注={currentBet}）");

        // 设置滑动条值
        moveT.text = targetAmount.ToString();

        // 执行叫价
        onEnd();
    }
}
